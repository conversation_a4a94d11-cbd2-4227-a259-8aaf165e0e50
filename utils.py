import os
import time
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException, WebDriverException

def setup_chrome_driver():
    """
    Setup and initialize Chrome WebDriver with appropriate options
    """
    print("[INFO] Setting up Chrome WebDriver...")
    chrome_options = Options()
    chrome_options.add_argument("--start-maximized")
    chrome_options.add_argument("--disable-notifications")
    chrome_options.add_argument("--disable-blink-features=AutomationControlled")
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option("useAutomationExtension", False)
    
    # Add options to handle CORS and security issues
    chrome_options.add_argument("--disable-web-security")
    chrome_options.add_argument("--allow-running-insecure-content")
    chrome_options.add_argument("--disable-features=IsolateOrigins,site-per-process")
    chrome_options.add_argument("--disable-site-isolation-trials")
    chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
    
    print("[INFO] Chrome options configured")
    
    # Initialize the Chrome driver
    print("[INFO] Initializing Chrome WebDriver...")
    driver = webdriver.Chrome(options=chrome_options)
    
    # Execute CDP commands to disable automation flags
    driver.execute_cdp_cmd("Page.addScriptToEvaluateOnNewDocument", {
        "source": """
        Object.defineProperty(navigator, 'webdriver', {get: () => undefined});
        delete window.cdc_adoQpoasnfa76pfcZLmcfl_Array;
        delete window.cdc_adoQpoasnfa76pfcZLmcfl_Promise;
        delete window.cdc_adoQpoasnfa76pfcZLmcfl_Symbol;
        """
    })
    
    driver.implicitly_wait(10)
    print("[SUCCESS] Chrome WebDriver initialized successfully")
    return driver

def take_screenshot(driver, filename="screenshot.png"):
    """
    Take a screenshot and save it to the specified filename
    """
    try:
        screenshot_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), filename)
        driver.save_screenshot(screenshot_path)
        print(f"Screenshot disimpan di: {screenshot_path}")
        return screenshot_path
    except Exception as e:
        print(f"Failed to take screenshot: {e}")
        return None

def safe_click(element, driver=None):
    """
    Attempt to click an element safely, falling back to JavaScript click if necessary
    """
    try:
        print("[INFO] Attempting to click element...")
        element.click()
        print("[SUCCESS] Clicked element")
        return True
    except Exception as e:
        print(f"[WARNING] Regular click failed: {e}")
        # Try JavaScript click as fallback if driver is provided
        if driver:
            try:
                print("[INFO] Attempting JavaScript click...")
                driver.execute_script("arguments[0].click();", element)
                print("[SUCCESS] JavaScript click successful")
                return True
            except Exception as js_e:
                print(f"[ERROR] JavaScript click also failed: {js_e}")
        return False

def find_element_with_multiple_selectors(parent_element, selectors, wait_time=3, timeout=10):
    """
    Try to find an element using multiple selectors
    
    Args:
        parent_element: WebDriver instance or WebElement to search within
        selectors: List of tuples (By, selector)
        wait_time: Time to wait for element to be present
        timeout: Maximum time to spend trying selectors
        
    Returns:
        WebElement if found, None otherwise
    """
    element = None
    start_time = time.time()
    
    for selector_type, selector in selectors:
        # Check if we've exceeded the timeout
        if time.time() - start_time > timeout:
            print(f"[WARNING] Timeout reached in find_element_with_multiple_selectors after {timeout} seconds")
            return None
            
        try:
            print(f"[DEBUG] Trying selector: {selector_type} - {selector}")
            # Check if parent_element is a WebDriver instance or WebElement
            if hasattr(parent_element, 'find_elements'):
                # It's a WebDriver instance
                element = WebDriverWait(parent_element, min(wait_time, timeout - (time.time() - start_time))).until(
                    EC.presence_of_element_located((selector_type, selector))
                )
            else:
                # It's a WebElement, use find_element directly
                elements = parent_element.find_elements(selector_type, selector)
                if elements:
                    element = elements[0]
                else:
                    continue
                    
            print(f"[SUCCESS] Found element with selector: {selector}")
            break
        except (TimeoutException, NoSuchElementException):
            continue
        except WebDriverException as e:
            print(f"[ERROR] WebDriverException: {e}")
            # Don't re-raise, just log and continue to next selector
            continue
    
    return element

def save_to_csv(data, fieldnames, filename, encoding='utf-8'):
    """
    Save data to a CSV file
    
    Args:
        data: List of dictionaries to save
        fieldnames: List of field names for CSV header
        filename: Name of the CSV file
        encoding: File encoding
        
    Returns:
        bool: True if successful, False otherwise
    """
    import csv
    try:
        print(f"[INFO] Saving {len(data)} records to {filename}...")
        with open(filename, 'w', newline='', encoding=encoding) as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            for row in data:
                writer.writerow(row)
        print(f"[SUCCESS] Successfully saved {len(data)} records to {filename}")
        return True
    except Exception as e:
        print(f"[ERROR] Error saving to CSV: {e}")
        return False