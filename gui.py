import sys
import os
import time
import io
import contextlib
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
                             QLabel, QLineEdit, QPushButton, QTextEdit, QCheckBox, 
                             QProgressBar, QMessageBox, QFileDialog, QComboBox, QStyleFactory)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QSettings
from PyQt5.QtGui import QIcon, QFont, QPalette, QColor

# Import custom modules
from main import JobStreetScraperMain

class ScraperThread(QThread):
    update_progress = pyqtSignal(str)
    update_status = pyqtSignal(int)
    finished = pyqtSignal(list)
    error = pyqtSignal(str)
    log_capture = pyqtSignal(str)  # New signal for detailed logs
    
    def __init__(self, email, password, job_page_link):
        super().__init__()
        self.email = email
        self.password = password
        self.job_page_link = job_page_link
        
    def emit_log(self, message):
        """Emit log message with timestamp"""
        timestamp = time.strftime("%H:%M:%S", time.localtime())
        self.log_capture.emit(f"[{timestamp}] {message}")
        
    def run(self):
        # Redirect stdout to capture print statements
        old_stdout = sys.stdout
        sys.stdout = io.StringIO()
        
        try:
            self.emit_log("=== MEMULAI PROSES SCRAPING ===")
            self.emit_log(f"Email: {self.email}")
            self.emit_log(f"Job Page: {self.job_page_link}")
            
            # Initialize scraper
            self.emit_log("Menginisialisasi scraper...")
            self.update_progress.emit("Initializing scraper...")
            scraper = JobStreetScraperMain(self.email, self.password, self.job_page_link)
            
            # Setup
            self.emit_log("Melakukan setup scraper...")
            self.update_progress.emit("Setting up scraper...")
            if not scraper.setup():
                self.error.emit("Failed to set up scraper")
                return
            
            # Login
            self.emit_log("Memulai proses login...")
            self.update_progress.emit("Logging in...")
            if not scraper.login_handler.login(scraper.login_url):
                self.error.emit("Login failed")
                return
            
            # Navigate to job page
            self.emit_log("Menuju ke halaman job...")
            self.update_progress.emit("Navigating to job page...")
            if not scraper.scraper.navigate_to_job_page(self.job_page_link):
                self.error.emit("Failed to navigate to job page")
                return
            
            # Scrape candidates
            self.emit_log("Memulai scraping data kandidat...")
            self.update_progress.emit("Scraping candidates...")
            page_candidates = scraper.scraper.scrape_candidates()
            self.emit_log(f"Ditemukan {len(page_candidates)} kandidat di halaman ini.")
            self.update_progress.emit(f"Found {len(page_candidates)} candidates.")
            
            # Update progress
            self.update_status.emit(100)
            
            # Get all scraped candidates
            candidates = scraper.scraper.get_candidates()
            self.emit_log(f"Total kandidat yang berhasil di-scrape: {len(candidates)}")
            self.update_progress.emit(f"Total candidates scraped: {len(candidates)}")
            
            # Show candidate details
            if candidates:
                self.emit_log("=== DATA KANDIDAT YANG DI-SCRAPE ===")
                for i, candidate in enumerate(candidates[:5]):  # Show first 5 candidates
                    self.emit_log(f"Kandidat {i+1}: Nama: {candidate.get('name', 'N/A')}, Telepon: {candidate.get('phone', 'N/A')}")
                if len(candidates) > 5:
                    self.emit_log(f"... dan {len(candidates) - 5} kandidat lainnya")
            
            # Save to CSV
            if candidates:
                csv_file = "candidates.csv"
                self.emit_log(f"Menyimpan {len(candidates)} kandidat ke file {csv_file}...")
                self.update_progress.emit(f"Saving {len(candidates)} candidates to {csv_file}...")
            
            # Clean up
            self.emit_log("Membersihkan resources...")
            scraper.cleanup()
            
            # Emit finished signal with candidates
            self.emit_log("=== PROSES SCRAPING SELESAI ===")
            self.finished.emit(candidates)
            
        except Exception as e:
            self.emit_log(f"ERROR: {str(e)}")
            import traceback
            self.emit_log(f"Traceback: {traceback.format_exc()}")
            self.error.emit(f"Error: {str(e)}")
        finally:
            # Restore stdout
            sys.stdout = old_stdout

class JobStreetScraperGUI(QMainWindow):
    def __init__(self):
        super().__init__()
        self.init_ui()
        self.load_settings()
        
    def init_ui(self):
        # Set window properties
        self.setWindowTitle("JobStreet Scraper")
        self.setGeometry(100, 100, 800, 600)
        
        # Create central widget and layout
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)
        
        # Create form layout for inputs
        form_layout = QVBoxLayout()
        
        # Email input
        email_layout = QHBoxLayout()
        email_label = QLabel("Email:")
        self.email_input = QLineEdit()
        self.email_input.setText("<EMAIL>") # Set default email
        email_layout.addWidget(email_label)
        email_layout.addWidget(self.email_input)
        form_layout.addLayout(email_layout)
        
        # Password input
        password_layout = QHBoxLayout()
        password_label = QLabel("Password:")
        self.password_input = QLineEdit()
        self.password_input.setEchoMode(QLineEdit.Password)
        self.password_input.setText("wa9iH&F8s?64") # Set default password
        password_layout.addWidget(password_label)
        password_layout.addWidget(self.password_input)
        form_layout.addLayout(password_layout)
        
        # Job Page Link input
        job_page_link_layout = QHBoxLayout()
        job_page_link_label = QLabel("Job Page Link:")
        self.job_page_link_input = QLineEdit()
        self.job_page_link_input.setText("https://employer.jobstreetexpress.com/id/jobs/14a78030-fdc6-4a29-9471-ea4e3d6e419d-1739259131543/details")
        job_page_link_layout.addWidget(job_page_link_label)
        job_page_link_layout.addWidget(self.job_page_link_input)
        form_layout.addLayout(job_page_link_layout)
        
        
        
        # Add form layout to main layout
        main_layout.addLayout(form_layout)
        
        # Add start button
        self.start_button = QPushButton("Start Scraping")
        self.start_button.clicked.connect(self.start_scraping)
        main_layout.addWidget(self.start_button)
        
        # Add progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setValue(0)
        main_layout.addWidget(self.progress_bar)
        
        # Add log output
        log_label = QLabel("Log:")
        main_layout.addWidget(log_label)
        
        self.log_output = QTextEdit()
        self.log_output.setReadOnly(True)
        main_layout.addWidget(self.log_output)
        
        # Initialize scraper thread
        self.scraper_thread = None
        
        # Show the window
        self.show()
    
    
    
    def apply_dark_theme(self):
        app = QApplication.instance()
        
        # Create dark palette
        dark_palette = QPalette()
        dark_palette.setColor(QPalette.Window, QColor(53, 53, 53))
        dark_palette.setColor(QPalette.WindowText, Qt.white)
        dark_palette.setColor(QPalette.Base, QColor(25, 25, 25))
        dark_palette.setColor(QPalette.AlternateBase, QColor(53, 53, 53))
        dark_palette.setColor(QPalette.ToolTipBase, Qt.white)
        dark_palette.setColor(QPalette.ToolTipText, Qt.white)
        dark_palette.setColor(QPalette.Text, Qt.white)
        dark_palette.setColor(QPalette.Button, QColor(53, 53, 53))
        dark_palette.setColor(QPalette.ButtonText, Qt.white)
        dark_palette.setColor(QPalette.BrightText, Qt.red)
        dark_palette.setColor(QPalette.Link, QColor(42, 130, 218))
        dark_palette.setColor(QPalette.Highlight, QColor(42, 130, 218))
        dark_palette.setColor(QPalette.HighlightedText, Qt.black)
        
        # Apply palette
        app.setPalette(dark_palette)
        
        # Apply stylesheet for additional customization
        app.setStyleSheet("""
            QWidget {
                background-color: #2b2b2b;
                color: #f0f0f0;
                font-family: "Segoe UI", "Helvetica Neue", Helvetica, Arial, sans-serif;
                font-size: 10pt;
            }
            QMainWindow {
                background-color: #2b2b2b;
            }
            QLabel {
                color: #f0f0f0;
            }
            QLineEdit {
                background-color: #3c3c3c;
                border: 1px solid #555555;
                border-radius: 4px;
                padding: 5px;
                color: #f0f0f0;
                selection-background-color: #0078d7;
            }
            QTextEdit {
                background-color: #3c3c3c;
                border: 1px solid #555555;
                border-radius: 4px;
                padding: 5px;
                color: #f0f0f0;
                selection-background-color: #0078d7;
            }
            QPushButton {
                background-color: #0078d7;
                color: #ffffff;
                border: none;
                border-radius: 4px;
                padding: 8px 15px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #005ea6;
            }
            QPushButton:pressed {
                background-color: #004c8c;
            }
            QCheckBox {
                color: #f0f0f0;
            }
            QCheckBox::indicator {
                width: 16px;
                height: 16px;
            }
            QCheckBox::indicator:unchecked {
                border: 1px solid #555555;
                background-color: #3c3c3c;
            }
            QCheckBox::indicator:checked {
                background-color: #0078d7;
                border: 1px solid #0078d7;
            }
            QProgressBar {
                border: 1px solid #555555;
                border-radius: 5px;
                text-align: center;
                color: #f0f0f0;
                background-color: #3c3c3c;
            }
            QProgressBar::chunk {
                background-color: #0078d7;
                border-radius: 5px;
            }
            QMessageBox {
                background-color: #2b2b2b;
                color: #f0f0f0;
            }
            QMessageBox QLabel {
                color: #f0f0f0;
            }
            QToolTip {
                color: #ffffff;
                background-color: #0078d7;
                border: 1px solid #0078d7;
                border-radius: 4px;
                padding: 3px;
            }
        """)
    
    
    
    def load_settings(self):
        settings = QSettings("JobStreetScraper", "GUI")
        self.apply_dark_theme()
    
    def log_message(self, message):
        self.log_output.append(message)
        # Scroll to bottom
        self.log_output.verticalScrollBar().setValue(
            self.log_output.verticalScrollBar().maximum()
        )
    
    def update_progress_bar(self, value):
        self.progress_bar.setValue(value)
    
    def start_scraping(self):
        # Get input values
        email = self.email_input.text()
        password = self.password_input.text()
        job_page_link = self.job_page_link_input.text()
        
        # Validate inputs
        if not email or not password:
            QMessageBox.warning(self, "Input Error", "Please enter both email and password.")
            return
        
        if not job_page_link:
            QMessageBox.warning(self, "Input Error", "Please enter a Job Page Link.")
            return
        
        # Disable start button and clear log
        self.start_button.setEnabled(False)
        self.log_output.clear()
        self.progress_bar.setValue(0)
        
        # Create and start scraper thread
        self.scraper_thread = ScraperThread(email, password, job_page_link)
        self.scraper_thread.update_progress.connect(self.log_message)
        self.scraper_thread.update_status.connect(self.update_progress_bar)
        self.scraper_thread.finished.connect(self.scraping_finished)
        self.scraper_thread.error.connect(self.scraping_error)
        self.scraper_thread.log_capture.connect(self.log_message)  # Connect detailed logs
        self.scraper_thread.start()
    
    def scraping_finished(self, candidates):
        # Re-enable start button
        self.start_button.setEnabled(True)
        
        # Show completion message
        self.log_message(f"Scraping completed! Found {len(candidates)} candidates.")
        self.progress_bar.setValue(100)
        
        # Show success message
        QMessageBox.information(self, "Scraping Complete", 
                               f"Successfully scraped {len(candidates)} candidates.\n"
                               f"Results saved to candidates.csv")
    
    def scraping_error(self, error_message):
        # Re-enable start button
        self.start_button.setEnabled(True)
        
        # Log error
        self.log_message(f"ERROR: {error_message}")
        
        # Show error message
        QMessageBox.critical(self, "Scraping Error", 
                            f"An error occurred during scraping:\n{error_message}")

def main():
    # Create application
    app = QApplication(sys.argv)
    
    # Set application style
    app.setStyle(QStyleFactory.create("Fusion"))
    
    # Create and show GUI
    gui = JobStreetScraperGUI()
    
    # Run application
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()