# JobStreet Express Scraper

Aplikasi ini dibuat untuk melakukan scraping data kandidat (nama dan nomor telepon) dari halaman JobStreet Express dan menyimpannya dalam format CSV. Kode telah distruktur ulang dengan memisahkan fungsi-fungsi ke dalam modul terpisah untuk memudahkan pemeliharaan dan pengembangan. Aplikasi ini juga dilengkapi dengan antarmuka pengguna grafis (GUI) dengan fitur dark mode.

## Struktur Proyek

Proyek ini terdiri dari beberapa modul terpisah:

- `main.py`: Entry point utama aplikasi (mode konsol)
- `gui.py`: Implementasi antarmuka pengguna grafis
- `run_gui.py`: Entry point untuk menjalankan aplikasi dengan GUI
- `login_handler.py`: Menangani proses login ke JobStreet Express
- `scraper.py`: Menangani proses scraping data kandidat
- `pagination_handler.py`: Menangani navigasi antar halaman hasil
- `utils.py`: <PERSON><PERSON><PERSON> fungsi-fungsi pembantu yang digunakan di berbagai modul
- `run_scraper.bat`: File batch untuk menjalankan aplikasi mode konsol
- `run_gui.bat`: File batch untuk menjalankan aplikasi dengan GUI
- `DOCUMENTATION.md`: Dokumentasi teknologi yang digunakan dan penjelasan error

## Persyaratan

Sebelum menggunakan aplikasi ini, pastikan Anda telah menginstal:

1. Python 3.6 atau lebih baru
2. Selenium WebDriver
3. Chrome Browser
4. ChromeDriver yang sesuai dengan versi Chrome Anda

## Instalasi

```bash
pip install selenium
```

Untuk menginstal ChromeDriver, Anda dapat mengunduhnya dari [situs resmi ChromeDriver](https://sites.google.com/chromium.org/driver/) dan memastikan bahwa versinya sesuai dengan versi Chrome yang Anda gunakan.

## Cara Penggunaan

### Mode Konsol

1. Pastikan semua persyaratan telah terpenuhi
2. Jalankan aplikasi dengan salah satu cara berikut:

   Menggunakan Python langsung:
   ```bash
   python main.py
   ```

   Atau menggunakan file batch yang disediakan:
   ```bash
   run_scraper.bat
   ```

3. Masukkan email dan password akun JobStreet Express Anda saat diminta

### Mode GUI

1. Pastikan semua persyaratan telah terpenuhi, termasuk PyQt5 (`pip install PyQt5`)
2. Jalankan aplikasi GUI dengan salah satu cara berikut:

   Menggunakan Python langsung:
   ```bash
   python run_gui.py
   ```

   Atau menggunakan file batch yang disediakan:
   ```bash
   run_gui.bat
   ```

3. Pada antarmuka GUI, masukkan:
   - Email akun JobStreet Express Anda
   - Password akun JobStreet Express Anda
   - Jumlah halaman yang ingin di-scrape
   - Aktifkan/nonaktifkan dark mode sesuai preferensi

4. Klik tombol "Start Scraping" untuk memulai proses scraping

## Hasil

Aplikasi akan menghasilkan file CSV dengan nama `candidates.csv` yang berisi dua kolom:
1. `name`: Nama kandidat
2. `phone`: Nomor telepon kandidat

## Catatan

- Aplikasi ini menggunakan Selenium WebDriver untuk mengotomatisasi browser Chrome
- Proses scraping mungkin memerlukan waktu tergantung pada jumlah kandidat dan kecepatan internet
- Struktur modular memudahkan pemeliharaan jika terjadi perubahan pada halaman JobStreet Express
- URL login yang digunakan adalah `https://employer.jobstreetexpress.com/id/login`

## Keuntungan Struktur Modular

1. **Pemeliharaan Lebih Mudah**: Jika ada perubahan pada satu aspek (misalnya login), hanya perlu memperbaiki modul terkait
2. **Debugging Lebih Efisien**: Kesalahan dapat diisolasi ke modul tertentu
3. **Pengembangan Lebih Cepat**: Fitur baru dapat ditambahkan tanpa mengubah seluruh kode
4. **Pengujian Lebih Baik**: Setiap modul dapat diuji secara terpisah

## Troubleshooting

Jika Anda mengalami masalah:

1. Pastikan kredensial login Anda benar
2. Periksa apakah URL lowongan masih valid
3. Pastikan ChromeDriver yang digunakan sesuai dengan versi Chrome Anda
4. Periksa file screenshot yang dihasilkan untuk debugging
5. Jika ada error pada modul tertentu, periksa file modul terkait:
   - Masalah login: periksa `login_handler.py`
   - Masalah scraping: periksa `scraper.py`
   - Masalah paginasi: periksa `pagination_handler.py`