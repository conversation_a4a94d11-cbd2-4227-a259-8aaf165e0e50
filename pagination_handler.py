import time
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException, WebDriverException

# Import utility functions
from utils import find_element_with_multiple_selectors, take_screenshot, safe_click

class PaginationHandler:
    def __init__(self, driver):
        """
        Initialize the pagination handler
        
        Args:
            driver: WebDriver instance
        """
        self.driver = driver
        self.current_page = 1
        self.has_next_page = True
    
    def go_to_next_page(self, max_retries=3):
        """
        Navigate to the next page
        
        Args:
            max_retries: Maximum number of retry attempts
            
        Returns:
            bool: True if successfully navigated to next page, False otherwise
        """
        if not self.has_next_page:
            print("No more pages to navigate to")
            return False
        
        retry_count = 0
        while retry_count < max_retries:
            try:
                print(f"Attempting to navigate to page {self.current_page + 1}...")
                
                # Find next page button using multiple selectors
                next_page_button = self._find_next_page_button()
                
                if not next_page_button:
                    print("Next page button not found. This might be the last page.")
                    self.has_next_page = False
                    take_screenshot(self.driver, f"last_page_{self.current_page}.png")
                    return False
                
                # Check if next button is disabled
                if self._is_next_button_disabled(next_page_button):
                    print("Next page button is disabled. This is the last page.")
                    self.has_next_page = False
                    return False
                
                # Click next page button
                print(f"Clicking next page button to go to page {self.current_page + 1}")
                
                # Use safe click utility function
                if not safe_click(self.driver, next_page_button):
                    print("Failed to click next page button")
                    retry_count += 1
                    if retry_count < max_retries:
                        print(f"Retrying... (attempt {retry_count+1}/{max_retries})")
                        time.sleep(2)  # Wait before retry
                        continue
                    else:
                        self.has_next_page = False
                        return False
                
                # Wait for page to load
                time.sleep(5)  # Initial wait
                
                # Verify page changed
                if self._verify_page_changed():
                    self.current_page += 1
                    print(f"Successfully navigated to page {self.current_page}")
                    return True
                else:
                    print("Page may not have changed after clicking next button")
                    retry_count += 1
                    if retry_count < max_retries:
                        print(f"Retrying... (attempt {retry_count+1}/{max_retries})")
                        time.sleep(2)  # Wait before retry
                        continue
                    else:
                        self.has_next_page = False
                        return False
                    
            except Exception as e:
                print(f"Error navigating to next page: {e}")
                import traceback
                print(traceback.format_exc())  # Print stack trace for debugging
                take_screenshot(self.driver, f"pagination_error_page_{self.current_page}.png")
                retry_count += 1
                if retry_count < max_retries:
                    print(f"Retrying... (attempt {retry_count+1}/{max_retries})")
                    time.sleep(2)  # Wait before retry
                    continue
                else:
                    self.has_next_page = False
                    return False
        
        return False
    
    def _find_next_page_button(self):
        """
        Find the next page button using multiple selectors
        
        Returns:
            WebElement if found, None otherwise
        """
        next_page_selectors = [
            (By.XPATH, "//button[contains(text(), 'Next')] | //button[contains(text(), 'Berikutnya')]"),
            (By.XPATH, "//a[contains(text(), 'Next')] | //a[contains(text(), 'Berikutnya')]"),
            (By.XPATH, "//button[contains(@class, 'next')] | //a[contains(@class, 'next')]"),
            (By.XPATH, "//button[contains(@aria-label, 'Next')] | //button[contains(@aria-label, 'Berikutnya')]"),
            (By.XPATH, "//button[.//i[contains(@class, 'arrow-right')]] | //button[.//span[contains(@class, 'arrow-right')]]"),
            (By.XPATH, "//button[.//i[contains(@class, 'chevron-right')]] | //button[.//span[contains(@class, 'chevron-right')]]"),
            (By.XPATH, "//li[contains(@class, 'next')] | //div[contains(@class, 'next')]"),
            (By.XPATH, "//button[.//svg[contains(@class, 'arrow')]] | //button[.//svg[contains(@class, 'chevron')]]"),
        ]
        
        return find_element_with_multiple_selectors(self.driver, next_page_selectors)
    
    def _is_next_button_disabled(self, next_button):
        """
        Check if the next button is disabled
        
        Args:
            next_button: WebElement representing the next button
            
        Returns:
            bool: True if disabled, False otherwise
        """
        try:
            # Check various attributes that might indicate a disabled button
            disabled_attrs = [
                next_button.get_attribute("disabled"),
                next_button.get_attribute("aria-disabled"),
                "disabled" in next_button.get_attribute("class") if next_button.get_attribute("class") else False
            ]
            
            return any(attr for attr in disabled_attrs if attr and attr != "false")
            
        except Exception as e:
            print(f"Error checking if next button is disabled: {e}")
            return False
    
    def _verify_page_changed(self):
        """
        Verify that the page has changed after clicking next
        
        Returns:
            bool: True if page changed, False otherwise
        """
        try:
            # Wait for page to load
            time.sleep(3)
            
            # Try to find page indicators
            page_indicators = [
                (By.XPATH, "//div[contains(@class, 'pagination')]"),
                (By.XPATH, "//ul[contains(@class, 'pagination')]"),
                (By.XPATH, "//div[contains(@class, 'page-number')] | //span[contains(@class, 'page-number')]"),
                (By.XPATH, "//div[contains(text(), 'Page')] | //div[contains(text(), 'Halaman')]"),
            ]
            
            indicator = find_element_with_multiple_selectors(self.driver, page_indicators)
            
            if indicator:
                # Check if the indicator text contains the current page number
                return str(self.current_page + 1) in indicator.text
            
            # If no indicator found, assume page changed
            return True
            
        except Exception as e:
            print(f"Error verifying page change: {e}")
            return False
    
    def has_more_pages(self):
        """
        Check if there are more pages to navigate to
        
        Returns:
            bool: True if there are more pages, False otherwise
        """
        return self.has_next_page
    
    def get_current_page(self):
        """
        Get the current page number
        
        Returns:
            int: Current page number
        """
        return self.current_page