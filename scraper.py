import time
import re
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException, WebDriverException
from selenium.webdriver import ActionChains

# Import utility functions
from utils import find_element_with_multiple_selectors, take_screenshot

class Scraper:
    def __init__(self, driver):
        """
        Initialize the scraper
        
        Args:
            driver: WebDriver instance
        """
        self.driver = driver
        self.candidates = []
    
    def navigate_to_job_page(self, job_page_link):
        """
        Navigate to the specified job page
        
        Args:
            job_page_link: URL of the job page
            
        Returns:
            bool: True if navigation successful, False otherwise
        """
        try:
            # Ensure URL is correct format (fix common errors in URL)
            if "jobstree.express" in job_page_link:
                # Fix incorrect domain if present
                job_page_link = job_page_link.replace("jobstree.express", "jobstreetexpress")
            
            print(f"[INFO] Navigating to job page: {job_page_link}")
            self.driver.get(job_page_link)
            time.sleep(5)  # Increased wait time for better loading
            print("[SUCCESS] Navigation completed")
            
            # Take screenshot after navigation for debugging
            take_screenshot(self.driver, "job_page_loaded.png")
            return True
                
        except Exception as e:
            print(f"[ERROR] Error navigating to job page: {e}")
            take_screenshot(self.driver, "job_page_error.png")
            return False
    
    def scrape_candidate_data(self):
        """
        Scrape candidate data from the job page
        
        Returns:
            list: List of candidate data dictionaries
        """
        try:
            print("[INFO] Memulai scraping data kandidat...")
            
            # Reduced wait time for faster processing
            time.sleep(2)
            
            # Take screenshot before scraping for debugging
            print("[DEBUG] Mengambil screenshot sebelum scraping...")
            take_screenshot(self.driver, "before_scraping.png")
            
            # Set a timeout for the entire scraping operation
            start_time = time.time()
            max_scraping_time = 60  # 1 minute max for scraping (reduced from 2 minutes)
            
            # Set timeout per candidate
            max_time_per_candidate = 5  # Maximum 5 seconds per candidate
            
            # Check page source for indicators first
            page_source = self.driver.page_source.lower()
            has_nama = 'nama' in page_source
            has_name = 'name' in page_source
            has_candidate = 'candidate' in page_source or 'applicant' in page_source
            
            print(f"[DEBUG] Page indicators - Nama: {has_nama}, Name: {has_name}, Candidate/Applicant: {has_candidate}")
            print(f"[DEBUG] Current URL: {self.driver.current_url}")
            
            # Try to find candidate elements
            candidate_elements = []
            try:
                # Try to find candidate elements with common selectors
                print("[DEBUG] Mencoba mencari elemen kandidat dengan selector umum...")
                candidate_elements = self.driver.find_elements(By.XPATH, "//div[contains(@class, 'candidate')] | //div[contains(@class, 'applicant')]")
                if not candidate_elements:
                    print("[DEBUG] Selector umum tidak menemukan elemen, mencoba selector alternatif...")
                    # Try alternative selectors
                    candidate_elements = self.driver.find_elements(By.XPATH, "//div[contains(@class, 'card')] | //div[contains(@class, 'list-item')]")
                    
                print(f"[DEBUG] Ditemukan {len(candidate_elements)} elemen kandidat dengan selector umum/alternatif")
            except Exception as e:
                print(f"[ERROR] Error finding candidate elements: {str(e)}")
                # Continue execution even if this fails
                
            # If no candidate elements found, try alternative approach
            if not candidate_elements:
                print("[WARNING] Tidak ditemukan elemen kandidat dengan selector, mencoba pendekatan alternatif...")
                try:
                    # Look for elements that might contain names (more aggressive approach)
                    name_pattern_elements = []
                    
                    # Set a timeout for this operation
                    alt_start_time = time.time()
                    alt_timeout = 3  # 3 seconds max for alternative search (reduced from 5)
                    
                    # Try specific approaches in sequence
                    try:
                        # Approach 1: Look for elements with specific text patterns that might be names
                        print("[INFO] Pendekatan 1: Mencari elemen dengan pola teks nama...")
                        name_elements = self.driver.find_elements(By.XPATH, 
                            "//div[string-length(normalize-space(text())) > 3 and string-length(normalize-space(text())) < 50]")
                        
                        if name_elements:
                            print(f"[INFO] Ditemukan {len(name_elements)} elemen dengan pola teks nama")
                            # Take only the first 20 elements to avoid processing too many
                            for element in name_elements[:20]:
                                if time.time() - alt_start_time > alt_timeout:
                                    print("[WARNING] Batas waktu pencarian alternatif tercapai")
                                    break
                                    
                                try:
                                    # Get parent element as candidate card
                                    parent = element.find_element(By.XPATH, "..")
                                    if parent not in name_pattern_elements:
                                        name_pattern_elements.append(parent)
                                except Exception as parent_e:
                                    # If can't get parent, use the element itself
                                    if element not in name_pattern_elements:
                                        name_pattern_elements.append(element)
                                    print(f"[DEBUG] Menggunakan elemen itu sendiri: {str(parent_e)[:50]}")
                        
                        # Approach 2: If still no elements, look for divs with flex classes (common in modern UIs)
                        if not name_pattern_elements:
                            print("[INFO] Pendekatan 2: Mencari div dengan class flex...")
                            flex_elements = self.driver.find_elements(By.XPATH, "//div[contains(@class, 'flex')]")
                            if flex_elements:
                                print(f"[INFO] Ditemukan {len(flex_elements)} elemen dengan class flex")
                                # Take only the first 20 elements
                                name_pattern_elements = flex_elements[:20]
                        
                        # Approach 3: Last resort - get all divs with some content
                        if not name_pattern_elements:
                            print("[INFO] Pendekatan 3: Mencari semua div dengan konten...")
                            content_elements = self.driver.find_elements(By.XPATH, "//div[normalize-space(.)]")
                            if content_elements:
                                print(f"[INFO] Ditemukan {len(content_elements)} div dengan konten")
                                # Take only the first 10 elements as last resort
                                name_pattern_elements = content_elements[:10]
                                
                    except Exception as e:
                        print(f"[ERROR] Error dalam pencarian alternatif: {str(e)[:100]}")
                        # Don't let exceptions stop the process
                        pass
                        
                    if name_pattern_elements:
                        print(f"[SUCCESS] Ditemukan {len(name_pattern_elements)} kandidat potensial dengan pendekatan alternatif")
                        candidate_elements = name_pattern_elements
                except Exception as e:
                    print(f"[ERROR] Error dalam pencarian kandidat alternatif: {str(e)[:100]}")
                    # Continue execution even if alternative search fails
                    pass
            
            # Optimized list of candidate selectors - most likely first
            candidate_selectors = [
                # Selectors spesifik untuk JobStreet Express
                (By.XPATH, "//div[contains(@class, 'applicant-card')]"),
                (By.XPATH, "//div[contains(@class, 'candidate-card')]"),
                (By.XPATH, "//div[contains(@class, 'candidate-item')]"),
                (By.XPATH, "//div[contains(@class, 'applicant-item')]"),
                (By.XPATH, "//div[contains(@class, 'candidate-list-item')]"),
                (By.XPATH, "//div[contains(@class, 'applicant-list-item')]"),
                (By.XPATH, "//div[contains(@class, 'flex flex-col gap-2')]"),
                (By.XPATH, "//div[contains(@class, 'flex flex-col')]"),
                (By.XPATH, "//div[contains(@class, 'flex items-center')]"),
                # Selectors berdasarkan atribut data
                (By.XPATH, "//div[@data-testid='candidate-card']"),
                (By.XPATH, "//div[@data-testid='applicant-card']"),
                (By.XPATH, "//div[@data-testid='candidate']"),
                (By.XPATH, "//div[@data-testid='applicant']"),
                (By.XPATH, "//div[@data-component='candidate']"),
                (By.XPATH, "//div[@data-component='applicant']"),
                # Selectors berdasarkan konten
                (By.XPATH, "//div[.//div[contains(text(), 'Nama')]]"),
                (By.XPATH, "//div[.//div[contains(text(), 'Name')]]"),
                (By.XPATH, "//div[.//div[contains(text(), 'Telepon')]]"),
                (By.XPATH, "//div[.//div[contains(text(), 'Phone')]]"),
                (By.XPATH, "//div[.//div[contains(text(), 'Email')]]"),
                # Selectors umum
                (By.XPATH, "//div[contains(@class, 'candidate')]"),
                (By.XPATH, "//div[contains(@class, 'applicant')]"),
                (By.XPATH, "//tr[contains(@class, 'candidate')]"),
                (By.XPATH, "//tr[contains(@class, 'applicant')]"),
                (By.XPATH, "//div[contains(@class, 'card')]"),
                (By.XPATH, "//div[contains(@class, 'profile')]"),
                (By.XPATH, "//div[contains(@class, 'job-seeker')]"),
                (By.XPATH, "//li[contains(@class, 'candidate')]"),
                (By.XPATH, "//li[contains(@class, 'applicant')]"),
                (By.XPATH, "//div[contains(@class, 'list-item')]"),
                (By.XPATH, "//div[contains(@class, 'item')]"),
                (By.XPATH, "//div[contains(@class, 'user')]"),
                (By.XPATH, "//div[contains(@class, 'row')]//div[contains(@class, 'name')]"),
                (By.XPATH, "//table//tr"),  # Table rows as fallback
                (By.XPATH, "//div[contains(@class, 'container')]//div[contains(text(), 'Nama')]/ancestor::div[1]")
            ]
            
            # Add more general selectors as last resort
            general_selectors = [
                (By.XPATH, "//div[contains(@class, 'row')]"),
                (By.XPATH, "//div[contains(@class, 'container')]/div"),
                (By.XPATH, "//div[contains(@class, 'content')]/div")
            ]
            candidate_selectors.extend(general_selectors)
            
            candidate_elements = []
            for i, (selector_type, selector_value) in enumerate(candidate_selectors):
                # Check if we've exceeded the timeout
                if time.time() - start_time > max_scraping_time:
                    print(f"[WARNING] Scraping timeout reached after trying {i} selectors")
                    break
                    
                try:
                    print(f"[DEBUG] Mencoba selector {i+1}: {selector_value}")
                    # Use a try-except with a very short implicit wait to speed up failures
                    original_timeout = self.driver.implicitly_wait(0.5)  # Reduced from 1 second to 0.5
                    elements = self.driver.find_elements(selector_type, selector_value)
                    self.driver.implicitly_wait(original_timeout)
                    
                    if elements:
                        candidate_elements = elements
                        print(f"[SUCCESS] Ditemukan {len(elements)} kandidat menggunakan selector: {selector_value}")
                        break
                except Exception as e:
                    print(f"[WARNING] Error dengan selector {selector_value}: {str(e)[:100]}")
                    # Reset implicit wait in case of exception
                    try:
                        self.driver.implicitly_wait(original_timeout)
                    except:
                        pass
                    continue
            
            if not candidate_elements:
                print("[WARNING] Tidak ditemukan elemen kandidat dengan selector standar")
                
                # Alternative approach: find elements containing name patterns
                if has_nama or has_name:
                    print("[INFO] Mencoba pendekatan alternatif - mencari elemen dengan pola nama...")
                    try:
                        # Set a timeout for this operation
                        alt_start_time = time.time()
                        alt_max_time = 30  # 30 seconds max for alternative approach
                        
                        name_elements = self.driver.find_elements(By.XPATH, "//*[contains(translate(text(), 'NAMA', 'nama'), 'nama') or contains(translate(text(), 'NAME', 'name'), 'name')]")
                        if name_elements:
                            print(f"[INFO] Ditemukan {len(name_elements)} elemen nama potensial")
                            # Get unique parent elements
                            parent_elements = set()
                            for elem in name_elements[:10]:  # Limit to first 10 to avoid duplicates
                                if time.time() - alt_start_time > alt_max_time:
                                    print("[WARNING] Timeout reached during alternative approach")
                                    break
                                    
                                try:
                                    parent = elem.find_element(By.XPATH, "./..")
                                    parent_elements.add(parent)
                                except:
                                    continue
                            candidate_elements = list(parent_elements)
                            print(f"[INFO] Ditemukan {len(candidate_elements)} elemen induk unik")
                    except Exception as e:
                        print(f"[ERROR] Error dalam pendekatan alternatif: {e}")
                
                # If still no elements, try a last resort approach - get all divs with some content
                if not candidate_elements:
                    print("[INFO] Mencoba pendekatan terakhir - mencari semua div dengan konten...")
                    try:
                        # Look for any div that might contain text
                        last_resort_elements = self.driver.find_elements(By.XPATH, "//div[string-length(normalize-space(text())) > 10]")
                        if last_resort_elements:
                            print(f"[INFO] Ditemukan {len(last_resort_elements)} elemen dengan konten")
                            # Limit to first 20 elements to avoid processing too many
                            candidate_elements = last_resort_elements[:20]
                    except Exception as e:
                        print(f"[ERROR] Error dalam pendekatan terakhir: {e}")
                
                if not candidate_elements:
                    print("[ERROR] Tidak ditemukan elemen kandidat sama sekali")
                    take_screenshot(self.driver, "no_candidates.png")
                    # Try to get page source for debugging
                    try:
                        with open("page_source.html", "w", encoding="utf-8") as f:
                            f.write(self.driver.page_source)
                        print("[DEBUG] Page source saved to page_source.html")
                    except Exception as e:
                        print(f"[ERROR] Failed to save page source: {e}")
                    return []
            
            print(f"[INFO] Total elemen kandidat yang akan diproses: {len(candidate_elements)}")
            
            # Limit processing to avoid timeout
            max_candidates = min(len(candidate_elements), 15)  # Reduced from 20 to 15 for faster processing
            print(f"[INFO] Akan memproses maksimal {max_candidates} kandidat")
            
            # Extract data from each candidate element
            successful_count = 0
            processed_count = 0
            extraction_start_time = time.time()
            extraction_timeout = 30  # Reduced from 60 to 30 seconds max for extraction
            
            for i, candidate_element in enumerate(candidate_elements[:max_candidates]):
                # Check if we've exceeded the extraction timeout
                if time.time() - extraction_start_time > extraction_timeout:
                    print(f"[WARNING] Extraction timeout reached after processing {i} candidates")
                    break
                    
                try:
                    processed_count += 1
                    print(f"[DEBUG] Memproses kandidat {i+1}/{max_candidates}")
                    # Set a timeout for each candidate extraction
                    candidate_start_time = time.time()
                    candidate_timeout = 2  # 2 seconds max per candidate (reduced from 3 seconds)
                    
                    # Check if processing this candidate is taking too long
                    if time.time() - candidate_start_time > max_time_per_candidate:
                        print(f"[WARNING] Skipping candidate {i+1} - took too long (>{max_time_per_candidate}s)")
                        continue
                        
                    # Add a small delay between candidates to avoid overloading the page
                    time.sleep(0.1)
                    
                    # Use a separate thread for extraction with timeout
                    from concurrent.futures import ThreadPoolExecutor, TimeoutError
                    with ThreadPoolExecutor() as executor:
                        future = executor.submit(self._extract_candidate_data, candidate_element)
                        try:
                            candidate_data = future.result(timeout=candidate_timeout)
                            if candidate_data:
                                self.candidates.append(candidate_data)
                                successful_count += 1
                                print(f"[SUCCESS] Berhasil ekstrak kandidat {i+1}: {candidate_data['name']}")
                            else:
                                print(f"[WARNING] Gagal ekstrak data dari kandidat {i+1}")
                        except TimeoutError:
                            print(f"[WARNING] Timeout extracting data from candidate {i+1} (>{candidate_timeout}s)")
                            # Try to extract just the name without phone number to avoid complete failure
                            try:
                                simple_name = self._extract_simple_name(candidate_element)
                                if simple_name:
                                    candidate_data = {"name": simple_name, "phone": ""}
                                    self.candidates.append(candidate_data)
                                    successful_count += 1
                                    print(f"[PARTIAL SUCCESS] Berhasil ekstrak nama kandidat {i+1}: {simple_name}")
                            except Exception as name_e:
                                print(f"[ERROR] Gagal ekstrak nama sederhana: {name_e}")
                            continue
                except Exception as e:
                    print(f"[ERROR] Error memproses kandidat {i+1}: {str(e)[:50]}...")
                    # Take screenshot for debugging
                    try:
                        take_screenshot(self.driver, f"candidate_error_{i+1}.png")
                    except:
                        pass
                    continue
            
            print(f"[INFO] Selesai scraping: {successful_count}/{processed_count} kandidat berhasil diekstrak dari {max_candidates} yang dicoba")
            print(f"[INFO] Waktu total ekstraksi: {time.time() - extraction_start_time:.2f} detik")
            
            # Jika tidak ada kandidat yang berhasil diekstrak, coba sekali lagi dengan pendekatan berbeda
            if successful_count == 0 and len(candidate_elements) > 0:
                print("[WARNING] Tidak ada kandidat yang berhasil diekstrak, mencoba pendekatan alternatif...")
                try:
                    # Coba ekstrak nama saja dari semua kandidat
                    for i, candidate_element in enumerate(candidate_elements[:5]):
                        try:
                            simple_name = self._extract_simple_name(candidate_element)
                            if simple_name:
                                self.candidates.append({"name": simple_name, "phone": ""})
                                print(f"[PARTIAL SUCCESS] Berhasil ekstrak nama kandidat {i+1}: {simple_name}")
                        except Exception as e:
                            print(f"[ERROR] Gagal ekstrak nama dengan pendekatan alternatif: {str(e)[:50]}...")
                except Exception as e:
                    print(f"[ERROR] Gagal dengan pendekatan alternatif: {str(e)[:50]}...")
            
            return self.candidates
            
        except Exception as e:
            print(f"[ERROR] Error scraping candidate data: {e}")
            import traceback
            print(f"[DEBUG] Traceback: {traceback.format_exc()}")
            take_screenshot(self.driver, "scraping_error.png")
            return []
    
    def _extract_candidate_data(self, candidate_element):
        """
        Extract data from a candidate element
        
        Args:
            candidate_element: WebElement representing a candidate
            
        Returns:
            dict: Candidate data dictionary
        """
        try:
            # Initialize candidate data
            candidate_data = {
                "name": "",
                "phone": ""
            }
            
            # Get all text from the candidate element for fallback
            element_text = candidate_element.text.lower()
            print(f"Element text: {element_text[:200]}...")  # Show first 200 chars
            
            # Extract name with extended selectors
            name_selectors = [
                # Selectors spesifik untuk jobstree.express.com
                (By.XPATH, ".//h2[contains(@class, 'inline-block font-light')]"),  # Specific selector from user
                (By.XPATH, ".//div[contains(@class, 'text-lg')]"),
                (By.XPATH, ".//div[contains(@class, 'font-semibold')]"),
                (By.XPATH, ".//div[contains(@class, 'font-bold')]"),
                (By.XPATH, ".//div[contains(@class, 'text-blue-500')]"),
                (By.XPATH, ".//div[contains(@class, 'text-blue-600')]"),
                (By.XPATH, ".//div[contains(@class, 'text-blue-700')]"),
                # Selectors umum
                (By.XPATH, ".//div[contains(@class, 'name')] | .//span[contains(@class, 'name')] | .//h3"),
                (By.XPATH, ".//div[contains(text(), 'Nama')] | .//span[contains(text(), 'Nama')]"),
                (By.XPATH, ".//h3 | .//h4 | .//h5"),
                (By.XPATH, ".//strong[contains(text(), 'Nama')]"),
                (By.XPATH, ".//b[contains(text(), 'Nama')]"),
                (By.XPATH, ".//div[contains(@class, 'title')]"),
                (By.XPATH, ".//span[contains(@class, 'title')]"),
                (By.XPATH, ".//div[contains(@class, 'fullname')]"),
                (By.XPATH, ".//span[contains(@class, 'fullname')]"),
                (By.XPATH, ".//a[contains(@class, 'name')]"),
                (By.XPATH, ".//a[contains(@class, 'profile')]"),
                (By.XPATH, ".//div[@data-testid='name']"),
                (By.XPATH, ".//span[@data-testid='name']")
            ]
            
            # Set timeout for name extraction
            name_start_time = time.time()
            name_timeout = 2  # Reduced from 3 to 2 seconds max for name extraction
            
            # Try to find name element with timeout
            name_element = None
            original_timeout = None
            for selector_type, selector_value in name_selectors:
                # Check if we've exceeded the timeout
                if time.time() - name_start_time > name_timeout:
                    print("[WARNING] Timeout reached during name extraction")
                    break
                    
                try:
                    # Use find_element with a shorter implicit wait
                    original_timeout = self.driver.implicitly_wait(0.3)  # Reduced implicit wait
                    elements = candidate_element.find_elements(selector_type, selector_value)
                    self.driver.implicitly_wait(original_timeout)
                    if elements:
                        name_element = elements[0]
                        break
                except Exception as e:
                    # Reset implicit wait in case of exception
                    if original_timeout is not None:
                        try:
                            self.driver.implicitly_wait(original_timeout)
                        except:
                            pass
                    print(f"[DEBUG] Error with name selector {selector_value}: {str(e)[:30]}")
                    continue
            
            if name_element:
                name_text = name_element.text.strip()
                # Clean name text (remove labels like "Nama:", "Name:", etc.)
                name_text = re.sub(r'^(nama|name):\s*', '', name_text, flags=re.IGNORECASE)
                candidate_data["name"] = name_text
                print(f"Found name: {name_text}")
            else:
                # Fallback: try to find name in element text (with timeout check)
                if time.time() - name_start_time <= name_timeout:
                    name_patterns = [
                        r'nama[:\s]+([^\n\r]+)',
                        r'name[:\s]+([^\n\r]+)',
                        r'([A-Z][a-z]+\s+[A-Z][a-z]+)'  # Simple name pattern
                    ]
                    for pattern in name_patterns:
                        if time.time() - name_start_time > name_timeout:
                            print("[WARNING] Timeout reached during regex name extraction")
                            break
                            
                        matches = re.findall(pattern, element_text, re.IGNORECASE)
                        if matches:
                            candidate_data["name"] = matches[0].strip()
                            print(f"Found name (fallback): {candidate_data['name']}")
                            break
            
            # Extract phone number with extended selectors
            phone_selectors = [
                # Selectors spesifik untuk jobstree.express.com
                (By.XPATH, ".//button[contains(@class, 'text-info text-base cursor-pointer hover:underline')]"),  # Specific selector from user
                (By.XPATH, ".//button[contains(@class, 'text-blue-500')]"),
                (By.XPATH, ".//div[contains(@class, 'text-blue-500')]"),
                # Selectors umum
                (By.XPATH, ".//div[contains(@class, 'phone')] | .//span[contains(@class, 'phone')]"),
                (By.XPATH, ".//div[contains(text(), 'Telepon')] | .//span[contains(text(), 'Telepon')]"),
                (By.XPATH, ".//div[contains(text(), 'HP')] | .//span[contains(text(), 'HP')]"),
                (By.XPATH, ".//div[contains(text(), 'No. HP')] | .//span[contains(text(), 'No. HP')]"),
                (By.XPATH, ".//div[contains(text(), 'Contact')] | .//span[contains(text(), 'Contact')]"),
                (By.XPATH, ".//a[contains(@href, 'tel:')]"),
                (By.XPATH, ".//div[@data-testid='phone']"),
                (By.XPATH, ".//span[@data-testid='phone']")
            ]
            
            # Set timeout for phone extraction
            phone_start_time = time.time()
            phone_timeout = 1.0  # Reduced from 1.5 to 1.0 seconds max for phone extraction
            
            # Try to find phone element with timeout
            phone_element = None
            original_timeout = None
            try:
                original_timeout = self.driver.implicitly_wait(0.2)  # Set even shorter implicit wait for faster failures
            except Exception as e:
                print(f"[WARNING] Error setting implicit wait: {str(e)[:50]}...")
            for selector_type, selector_value in phone_selectors:
                # Check if we've exceeded the timeout
                if time.time() - phone_start_time > phone_timeout:
                    print("[WARNING] Timeout reached during phone extraction")
                    break
                    
                try:
                    elements = candidate_element.find_elements(selector_type, selector_value)
                    if elements:
                        phone_element = elements[0]
                        break
                except Exception as e:
                    continue
            # Reset implicit wait
                try:
                    if original_timeout is not None:
                        self.driver.implicitly_wait(original_timeout)
                    else:
                        self.driver.implicitly_wait(0)  # Default to 0 if original_timeout is None
                except Exception as e:
                    print(f"[WARNING] Error resetting implicit wait: {str(e)[:50]}...")
                    self.driver.implicitly_wait(0)  # Fallback to 0
            
            if phone_element:
                try:
                    # Check if this is a button that needs to be clicked
                    if phone_element.tag_name.lower() in ['button', 'a'] or (phone_element.get_attribute('class') and 'click' in phone_element.get_attribute('class').lower()):
                        print("[INFO] Found phone button or clickable element, attempting to click it...")
                        # Get the text before clicking (might already contain the phone number)
                        phone_text_before = phone_element.text.strip()
                        
                        # Try to click the button to reveal the phone number
                        try:
                            # Scroll to the element first to ensure it's visible
                            self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", phone_element)
                            time.sleep(0.2)  # Further reduced wait time for scrolling
                            
                            # Try to click with JavaScript first (more reliable)
                            try:
                                self.driver.execute_script("arguments[0].click();", phone_element)
                                print("[INFO] Clicked phone button with JavaScript")
                            except Exception as js_e:
                                print(f"[WARNING] JS click failed: {str(js_e)[:30]}... trying normal click")
                                # Fallback to regular click
                                phone_element.click()
                                print("[INFO] Clicked phone button with normal click")
                                
                            time.sleep(0.3)  # Further reduced wait for dialog
                            
                            # Try to find the phone number in the dialog that appears
                            try:
                                # Look for the dialog element with shorter timeout
                                dialog_selectors = [
                                    "//dialog[contains(@class, 'modal')]", 
                                    "//div[contains(@class, 'modal')]",
                                    "//div[contains(@class, 'popup')]",
                                    "//div[@role='dialog']",
                                    "//div[contains(@class, 'fixed')][contains(@class, 'z-')]",
                                    "//div[contains(@style, 'position: fixed')]"
                                ]
                                
                                dialog = None
                                for selector in dialog_selectors:
                                    try:
                                        dialog = WebDriverWait(self.driver, 1).until(
                                            EC.presence_of_element_located((By.XPATH, selector))
                                        )
                                        print(f"[INFO] Found dialog element with selector: {selector}")
                                        break
                                    except:
                                        continue
                                
                                if dialog:
                                    # Look for phone number in the dialog
                                    phone_in_dialog = dialog.text
                                    candidate_data["phone"] = self._extract_phone_number(phone_in_dialog)
                                    print(f"[INFO] Found phone from dialog: {candidate_data['phone']}")
                                    
                                    # Close the dialog by clicking outside or on close button
                                    try:
                                        close_button = dialog.find_element(By.XPATH, ".//button[contains(@class, 'cursor-pointer') or contains(@class, 'close') or contains(@aria-label, 'close') or contains(text(), 'Close') or contains(text(), 'Tutup') or contains(text(), 'OK')]")
                                        self.driver.execute_script("arguments[0].click();", close_button)
                                        print("[INFO] Closed dialog using close button")
                                    except:
                                        # Click outside the dialog to close it
                                        actions = ActionChains(self.driver)
                                        actions.move_by_offset(10, 10).click().perform()
                                        print("[INFO] Attempted to close dialog by clicking outside")
                                else:
                                    print("[WARNING] No dialog found after clicking phone button")
                            except Exception as e:
                                print(f"[WARNING] Error finding dialog: {str(e)[:50]}")
                                # If we couldn't find the dialog, use the button text as fallback
                                if not candidate_data["phone"]:
                                    candidate_data["phone"] = self._extract_phone_number(phone_text_before)
                        except Exception as e:
                            print(f"[WARNING] Error clicking phone button: {str(e)[:50]}")
                            # Use the text from the button as fallback
                            candidate_data["phone"] = self._extract_phone_number(phone_text_before)
                    else:
                        # Regular element (not a button)
                        phone_text = phone_element.text.strip()
                        # Clean phone text (remove labels like "Telepon:", "HP:", etc.)
                        phone_text = re.sub(r'^(telepon|hp|no\. hp|contact):\s*', '', phone_text, flags=re.IGNORECASE)
                        candidate_data["phone"] = self._extract_phone_number(phone_text)
                    
                    print(f"Found phone: {candidate_data['phone']}")
                except Exception as e:
                    print(f"[ERROR] Error processing phone element: {str(e)[:50]}")
                    # Try to get text as fallback
                    try:
                        phone_text = phone_element.text.strip()
                        candidate_data["phone"] = self._extract_phone_number(phone_text)
                    except:
                        pass
            
            # If no phone element found, try to find it in the entire candidate element text (with timeout check)
            if not candidate_data["phone"] and time.time() - phone_start_time <= phone_timeout:
                candidate_data["phone"] = self._extract_phone_number(candidate_element.text)
                if candidate_data["phone"]:
                    print(f"Found phone (fallback): {candidate_data['phone']}")
            
            # Only return candidate data if we have at least a name
            if candidate_data["name"]:
                return candidate_data
            else:
                print("No name found for candidate, skipping...")
                return None
            
        except Exception as e:
            print(f"Error extracting candidate data: {e}")
            import traceback
            print(traceback.format_exc())
            return None
    
    def _extract_simple_name(self, element):
        """Extract just the name from an element using simplified approach"""
        try:
            # Try to find any text that looks like a name
            element_text = element.text.strip()
            
            # If element has no text, try to find child elements with text
            if not element_text:
                for child in element.find_elements(By.XPATH, ".//*"):
                    child_text = child.text.strip()
                    if child_text and len(child_text) > 3 and len(child_text) < 50:
                        # Avoid elements that look like phone numbers or emails
                        if not re.search(r'\d{5,}|@|http|\.com', child_text):
                            return child_text
                return ""
            
            # If element has text, try to extract name from it
            lines = element_text.split('\n')
            for line in lines:
                line = line.strip()
                if line and len(line) > 3 and len(line) < 50:
                    # Avoid lines that look like phone numbers or emails
                    if not re.search(r'\d{5,}|@|http|\.com', line):
                        return line
            
            # If no suitable text found, return the first line as fallback
            if lines and lines[0].strip():
                return lines[0].strip()
                
            return ""
        except Exception as e:
            print(f"Error extracting simple name: {e}")
            return ""
            
    def _extract_phone_number(self, text):
        """
        Extract phone number from text using regex
        
        Args:
            text: Text to extract phone number from
            
        Returns:
            str: Extracted phone number or empty string if not found
        """
        try:
            print(f"[DEBUG] Extracting phone from text: {text[:100]}...")
            
            # Pattern for Indonesian phone numbers
            # Matches formats like: +62812345678, 08123456789, 628123456789, etc.
            # Also matches formats with spaces or dashes like: +62 831 4101 7137
            patterns = [
                # Specific patterns for jobstree.express.com
                r'\+62[\s\-]?8\d{2}[\s\-]?\d{4}[\s\-]?\d{4}',  # +62 8xx format (most common)
                r'0?8\d{2}[\s\-]?\d{4}[\s\-]?\d{4}',  # 08xx format (most common)
                # General patterns
                r'\+62[\s\-]?\d{3}[\s\-]?\d{4}[\s\-]?\d{4}',  # +62 format with possible spaces/dashes
                r'\+62\d{9,12}',  # +62 format
                r'0\d{9,11}',     # 0 format
                r'62\d{9,12}',    # 62 format without +
                r'\d{3}[\s\-]?\d{4}[\s\-]?\d{4}'  # Just the numbers with possible spaces/dashes
            ]
            
            for pattern in patterns:
                matches = re.findall(pattern, text)
                if matches:
                    # Clean up the phone number (remove spaces, dashes, etc.)
                    phone = matches[0]
                    
                    # Standardize format
                    phone = re.sub(r'[\s\-\(\)]+', '', phone)  # Remove spaces, dashes, parentheses
                    
                    # Ensure consistent format (convert to +62 format if possible)
                    if phone.startswith('0'):
                        phone = '+62' + phone[1:]  # Convert 08xx to +628xx
                    elif phone.startswith('62'):
                        phone = '+' + phone  # Convert 62xx to +62xx
                    # Remove spaces and dashes
                    phone = re.sub(r'[\s\-]', '', phone)
                    print(f"[DEBUG] Extracted phone number: {phone}")
                    return phone
            
            # If no match found with the patterns, try to find any sequence of digits that looks like a phone number
            # This is a more aggressive approach and should be used as a last resort
            digit_sequences = re.findall(r'\d{8,15}', text)
            if digit_sequences:
                for seq in digit_sequences:
                    # Check if it looks like an Indonesian phone number
                    if len(seq) >= 10 and (seq.startswith('62') or seq.startswith('0')):
                        print(f"[DEBUG] Found potential phone number (fallback): {seq}")
                        return seq
            
            print("[DEBUG] No phone number found in text")
            return ""
        except Exception as e:
            print(f"[ERROR] Error extracting phone number: {str(e)[:50]}...")
            # Ensure we don't get stuck by returning empty string
            return ""
        finally:
            # Ensure we reset any timeouts or waits
            try:
                self.driver.implicitly_wait(0)
            except:
                pass
    
    def get_candidates(self):
        """
        Get the list of scraped candidates
        
        Returns:
            list: List of candidate data dictionaries
        """
        return self.candidates