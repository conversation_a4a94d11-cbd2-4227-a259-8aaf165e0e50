import time
import re
import logging
from typing import List, Dict, Optional
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException, WebDriverException
from selenium.webdriver import Action<PERSON>hains

# Import utility functions
from utils import take_screenshot

class JobStreetScraper:
    def __init__(self, driver):
        """
        Initialize the JobStreet scraper
        
        Args:
            driver: WebDriver instance
        """
        self.driver = driver
        self.candidates: List[Dict[str, str]] = []
        self.logger = self._setup_logging()
        
    def _setup_logging(self) -> logging.Logger:
        """Setup logging configuration"""
        logger = logging.getLogger('JobStreetScraper')
        logger.setLevel(logging.INFO)
        
        # Create console handler if not already exists
        if not logger.handlers:
            handler = logging.StreamHandler()
            handler.setLevel(logging.INFO)
            formatter = logging.Formatter('[%(levelname)s] %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            
        return logger
    
    def navigate_to_job_page(self, job_page_link: str) -> bool:
        """
        Navigate to the specified job page
        
        Args:
            job_page_link: URL of the job page
            
        Returns:
            bool: True if navigation successful, False otherwise
        """
        try:
            # Ensure URL is correct format (fix common errors in URL)
            if "jobstree.express" in job_page_link:
                # Fix incorrect domain if present
                job_page_link = job_page_link.replace("jobstree.express", "jobstreetexpress")
            
            self.logger.info(f"Navigating to job page: {job_page_link}")
            self.driver.get(job_page_link)
            
            # Wait for page to load
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            time.sleep(3)  # Additional wait for dynamic content
            
            self.logger.info("Navigation completed successfully")
            take_screenshot(self.driver, "job_page_loaded.png")
            return True
                
        except Exception as e:
            self.logger.error(f"Error navigating to job page: {e}")
            take_screenshot(self.driver, "job_page_error.png")
            return False
    
    def scrape_candidates(self) -> List[Dict[str, str]]:
        """
        Scrape candidate data from all pages

        Returns:
            list: List of candidate data dictionaries
        """
        try:
            self.logger.info("Starting candidate scraping...")
            take_screenshot(self.driver, "before_scraping.png")

            # Keep track of seen names to avoid duplicates
            seen_names = set()
            consecutive_empty_pages = 0
            max_empty_pages = 2  # Stop if we get 2 consecutive pages with no new candidates

            # Process all pages
            page_number = 1
            while True:
                self.logger.info(f"Processing page {page_number}")

                # Scrape candidates from current page
                page_candidates = self._scrape_current_page()

                # Filter out duplicates
                new_candidates = []
                for candidate in page_candidates:
                    name = candidate.get('name', '').strip()
                    if name and name not in seen_names:
                        seen_names.add(name)
                        new_candidates.append(candidate)
                        self.candidates.append(candidate)

                self.logger.info(f"Found {len(page_candidates)} candidates on page {page_number}")
                self.logger.info(f"New unique candidates: {len(new_candidates)}")
                self.logger.info(f"Total unique candidates so far: {len(self.candidates)}")

                # Check if we got any new candidates
                if len(new_candidates) == 0:
                    consecutive_empty_pages += 1
                    self.logger.warning(f"No new candidates found on page {page_number} ({consecutive_empty_pages}/{max_empty_pages})")

                    if consecutive_empty_pages >= max_empty_pages:
                        self.logger.info("Stopping due to consecutive pages with no new candidates")
                        break
                else:
                    consecutive_empty_pages = 0  # Reset counter

                # Check if there's a next page
                if not self._navigate_to_next_page():
                    self.logger.info("No more pages to process")
                    break

                page_number += 1
                time.sleep(2)  # Brief pause between pages

                # Safety limit to prevent infinite loops
                if page_number > 20:
                    self.logger.warning("Reached maximum page limit (20), stopping")
                    break

            self.logger.info(f"Scraping completed. Total unique candidates: {len(self.candidates)}")
            return self.candidates

        except Exception as e:
            self.logger.error(f"Error during scraping: {e}")
            take_screenshot(self.driver, "scraping_error.png")
            return self.candidates
    
    def _scrape_current_page(self) -> List[Dict[str, str]]:
        """
        Scrape candidates from the current page
        
        Returns:
            list: List of candidate data from current page
        """
        page_candidates = []
        
        try:
            # Wait for candidates to load
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.TAG_NAME, "article"))
            )
            
            # Find all candidate articles
            candidate_articles = self.driver.find_elements(By.TAG_NAME, "article")
            self.logger.info(f"Found {len(candidate_articles)} candidate articles on current page")
            
            for i, article in enumerate(candidate_articles, 1):
                try:
                    self.logger.debug(f"Processing candidate {i}/{len(candidate_articles)}")
                    candidate_data = self._extract_candidate_from_article(article)
                    
                    if candidate_data and candidate_data.get('name'):
                        page_candidates.append(candidate_data)
                        self.logger.info(f"Successfully extracted: {candidate_data['name']}")
                    else:
                        self.logger.warning(f"Failed to extract data from candidate {i}")
                        
                except Exception as e:
                    self.logger.error(f"Error processing candidate {i}: {e}")
                    continue
                    
        except TimeoutException:
            self.logger.error("Timeout waiting for candidate articles to load")
        except Exception as e:
            self.logger.error(f"Error scraping current page: {e}")
            
        return page_candidates
    
    def _extract_candidate_from_article(self, article) -> Optional[Dict[str, str]]:
        """
        Extract candidate data from an article element
        
        Args:
            article: WebElement representing a candidate article
            
        Returns:
            dict: Candidate data or None if extraction fails
        """
        try:
            candidate_data = {
                "name": "",
                "phone": ""
            }
            
            # Extract name
            name = self._extract_name_from_article(article)
            if not name:
                self.logger.warning("No name found in article")
                return None
                
            candidate_data["name"] = name
            
            # Extract phone number
            phone = self._extract_phone_from_article(article)
            candidate_data["phone"] = phone
            
            return candidate_data
            
        except Exception as e:
            self.logger.error(f"Error extracting candidate from article: {e}")
            return None
    
    def _extract_name_from_article(self, article) -> str:
        """
        Extract candidate name from article element

        Args:
            article: WebElement representing a candidate article

        Returns:
            str: Candidate name or empty string if not found
        """
        try:
            # Look for heading elements (level 2) which contain names
            headings = article.find_elements(By.XPATH, ".//h1 | .//h2 | .//h3 | .//h4 | .//h5 | .//h6")

            for heading in headings:
                text = heading.text.strip()
                if text and len(text) > 1 and len(text) < 100:
                    # Skip non-name text patterns
                    skip_patterns = [
                        r'seberapa besar kemungkinan',
                        r'merekomendasikan',
                        r'jobstreet',
                        r'teman atau kolega',
                        r'^\d+$',  # Pure numbers
                        r'^[^\w\s]+$',  # Pure symbols
                        r'@',  # Email addresses
                        r'http',  # URLs
                        r'\.com',  # Domains
                    ]

                    should_skip = False
                    for pattern in skip_patterns:
                        if re.search(pattern, text.lower()):
                            should_skip = True
                            break

                    if not should_skip:
                        # Skip single letters or very short text that might be initials only
                        if len(text) > 2 or (len(text) <= 2 and text.isalpha() and text.isupper()):
                            # Clean the name
                            name = re.sub(r'^(nama|name):\s*', '', text, flags=re.IGNORECASE)
                            if name:
                                return name

            self.logger.debug("No suitable name found in headings")
            return ""

        except Exception as e:
            self.logger.error(f"Error extracting name: {e}")
            return ""

    def _extract_phone_from_article(self, article) -> str:
        """
        Extract phone number from article element

        Args:
            article: WebElement representing a candidate article

        Returns:
            str: Phone number or empty string if not found
        """
        try:
            # Look for phone button with "Lihat telepon" text
            phone_buttons = article.find_elements(By.XPATH, ".//button[contains(text(), 'Lihat telepon')]")

            if phone_buttons:
                phone_button = phone_buttons[0]
                self.logger.debug("Found 'Lihat telepon' button, clicking to reveal phone number")

                try:
                    # Scroll to button to ensure it's visible
                    self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", phone_button)
                    time.sleep(0.5)

                    # Click the button to reveal phone number
                    phone_button.click()
                    time.sleep(1)  # Wait for phone number to be revealed

                    # Look for the revealed phone number (it becomes a button with the full number)
                    revealed_phone_buttons = article.find_elements(By.XPATH, ".//button[contains(text(), '+62') or contains(text(), '08') or contains(text(), '62')]")

                    for revealed_button in revealed_phone_buttons:
                        phone_text = revealed_button.text.strip()
                        if phone_text and (phone_text.startswith('+62') or phone_text.startswith('08') or phone_text.startswith('62')):
                            # Clean and format the phone number
                            phone_number = self._clean_phone_number(phone_text)
                            if phone_number:
                                self.logger.debug(f"Successfully extracted phone: {phone_number}")
                                return phone_number

                    self.logger.warning("Phone button clicked but no revealed phone number found")

                except Exception as click_error:
                    self.logger.warning(f"Error clicking phone button: {click_error}")

            # Fallback: look for any visible phone numbers in the article
            article_text = article.text
            phone_number = self._extract_phone_from_text(article_text)
            if phone_number:
                self.logger.debug(f"Found phone number in article text: {phone_number}")
                return phone_number

            self.logger.debug("No phone number found in article")
            return ""

        except Exception as e:
            self.logger.error(f"Error extracting phone: {e}")
            return ""

    def _clean_phone_number(self, phone_text: str) -> str:
        """
        Clean and format phone number

        Args:
            phone_text: Raw phone number text

        Returns:
            str: Cleaned phone number
        """
        if not phone_text:
            return ""

        # Remove extra whitespace and common separators
        phone = re.sub(r'[\s\-\(\)]+', '', phone_text.strip())

        # Ensure it starts with +62 format
        if phone.startswith('08'):
            phone = '+62' + phone[1:]
        elif phone.startswith('62') and not phone.startswith('+62'):
            phone = '+' + phone

        return phone

    def _extract_phone_from_text(self, text: str) -> str:
        """
        Extract phone number from text using regex patterns

        Args:
            text: Text to search for phone numbers

        Returns:
            str: Found phone number or empty string
        """
        if not text:
            return ""

        # Indonesian phone number patterns
        patterns = [
            r'\+62[\s\-]?8\d{2}[\s\-]?\d{4}[\s\-]?\d{4}',  # +62 8xx format
            r'0?8\d{2}[\s\-]?\d{4}[\s\-]?\d{4}',  # 08xx format
            r'\+62\d{9,12}',  # +62 format
            r'62\d{9,12}',    # 62 format without +
        ]

        for pattern in patterns:
            matches = re.findall(pattern, text)
            if matches:
                return self._clean_phone_number(matches[0])

        return ""

    def _navigate_to_next_page(self) -> bool:
        """
        Navigate to the next page of candidates

        Returns:
            bool: True if successfully navigated to next page, False if no more pages
        """
        try:
            # Store current URL to detect if navigation actually happened
            current_url = self.driver.current_url

            # Look for pagination controls
            pagination_buttons = self.driver.find_elements(By.XPATH, "//nav//button[not(@disabled)]")

            # Find the current page button
            current_page_buttons = self.driver.find_elements(By.XPATH, "//nav//button[@aria-current='page' or contains(@class, 'active')]")

            if current_page_buttons:
                current_page_text = current_page_buttons[0].text.strip()
                try:
                    current_page_num = int(current_page_text)
                    next_page_num = current_page_num + 1

                    # Look for the next page button
                    next_page_buttons = self.driver.find_elements(By.XPATH, f"//nav//button[text()='{next_page_num}' and not(@disabled)]")

                    if next_page_buttons:
                        next_button = next_page_buttons[0]
                        self.logger.info(f"Navigating to page {next_page_num}")

                        # Scroll to button and click
                        self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", next_button)
                        time.sleep(1)
                        next_button.click()

                        # Wait for new page to load and URL to change
                        time.sleep(3)

                        # Check if URL actually changed or if we're on a new page
                        new_url = self.driver.current_url
                        if new_url == current_url:
                            # URL didn't change, check if page content changed
                            try:
                                # Wait for articles to reload
                                WebDriverWait(self.driver, 5).until(
                                    EC.presence_of_element_located((By.TAG_NAME, "article"))
                                )

                                # Verify we're actually on the new page by checking the active page button
                                new_current_page_buttons = self.driver.find_elements(By.XPATH, "//nav//button[@aria-current='page' or contains(@class, 'active')]")
                                if new_current_page_buttons:
                                    new_page_text = new_current_page_buttons[0].text.strip()
                                    if new_page_text == str(next_page_num):
                                        return True
                                    else:
                                        self.logger.warning(f"Expected page {next_page_num} but got page {new_page_text}")
                                        return False

                            except TimeoutException:
                                self.logger.warning("Timeout waiting for new page to load")
                                return False
                        else:
                            # URL changed, verify articles are present
                            try:
                                WebDriverWait(self.driver, 10).until(
                                    EC.presence_of_element_located((By.TAG_NAME, "article"))
                                )
                                return True
                            except TimeoutException:
                                self.logger.warning("No articles found on new page")
                                return False
                    else:
                        self.logger.info(f"No button found for page {next_page_num}")
                        return False

                except ValueError:
                    self.logger.warning(f"Could not parse current page number: {current_page_text}")

            self.logger.info("No next page found")
            return False

        except Exception as e:
            self.logger.error(f"Error navigating to next page: {e}")
            return False

    def get_candidates(self) -> List[Dict[str, str]]:
        """
        Get the list of scraped candidates

        Returns:
            list: List of candidate data dictionaries
        """
        return self.candidates

# For backward compatibility, create an alias to the old class name
Scraper = JobStreetScraper
