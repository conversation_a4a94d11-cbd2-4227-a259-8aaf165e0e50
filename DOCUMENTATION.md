# JobStreet Scraper - Dokumentasi Teknologi

## Kredensial Login

```
URL: https://employer.jobstreetexpress.com/id/login
Email: <EMAIL>
Password: wa9iH&F8s?64
scrape link page: https://employer.jobstreetexpress.com/id/jobs/14a78030-fdc6-4a29-9471-ea4e3d6e419d-1739259131543/details
```

> Catatan: Ganti kredensial di atas dengan kredensial yang valid untuk mengakses sistem.

## Teknologi yang Digunakan

### 1. Bahasa Pemrograman
- **Python**: Bahasa pemrograman utama yang digunakan untuk mengembangkan aplikasi scraper.

### 2. Web Scraping
- **Selenium**: Framework otomatisasi browser yang digunakan untuk melakukan scraping data dari situs JobStreet.
- **ChromeDriver**: Driver browser yang digunakan oleh Selenium untuk mengontrol browser Chrome.

### 3. Antarmuka Pengguna (GUI)
- **PyQt5**: Framework GUI yang digunakan untuk membuat antarmuka pengguna dengan fitur dark mode.
- **QThread**: Komponen PyQt5 yang digunakan untuk menjalankan proses scraping di thread terpisah agar GUI tetap responsif.

### 4. Penyimpanan Data
- **CSV**: Format file yang digunakan untuk menyimpan data kandidat yang di-scrape.

### 5. Struktur Proyek
- **Modular Design**: Proyek diorganisir dalam modul-modul terpisah untuk memudahkan pemeliharaan dan pengembangan.

## Penjelasan Error dan Log Terminal

### Error yang Mungkin Muncul

#### 1. WebDriver Initialization Errors
```
Error setting up scraper: Message: unknown error: cannot find Chrome binary
```
- **Penyebab**: Chrome tidak terinstal atau tidak ditemukan di lokasi default.
- **Solusi**: Pastikan Chrome terinstal dan path-nya benar.

#### 2. Login Errors
```
Login failed. Please check your credentials.
```
- **Penyebab**: Email atau password yang dimasukkan salah, atau format input tidak sesuai.
- **Solusi**: Periksa kembali email dan password yang dimasukkan.

```
Setelah klik login masih di halaman login meskipun username dan password benar
```
- **Penyebab**: Masalah ini dapat disebabkan oleh beberapa faktor:
  1. Tombol login tidak benar-benar diklik karena masalah dengan event handler JavaScript
  2. Form tidak disubmit dengan benar
  3. Situs menggunakan mekanisme anti-bot yang mendeteksi Selenium
  
- **Solusi yang diimplementasikan**:
  - Menggunakan multiple metode klik (normal click, JavaScript click, Action Chains)
  - Mencoba submit form langsung jika tombol klik gagal
  - Mencoba menekan Enter pada field password
  - Menonaktifkan fitur deteksi otomasi di Chrome
  - Menambahkan opsi Chrome untuk mengatasi masalah CORS dan keamanan

#### 3. Navigation Errors
```
Failed to navigate to job page
```
- **Penyebab**: Struktur situs berubah atau koneksi internet terputus.
- **Solusi**: Periksa koneksi internet dan pastikan situs JobStreet dapat diakses.

#### 4. Selenium Exceptions
```
TimeoutException: Message: timeout: Timed out receiving message from renderer
```

#### 5. Chrome Automation Warning
```
Chrome is being controlled by automated test software
```
- **Penyebab**: Chrome mendeteksi bahwa browser dikendalikan oleh software otomatis (Selenium) dan menampilkan pesan peringatan.
- **Solusi**: Menggunakan opsi ChromeDriver tambahan untuk menyembunyikan tanda-tanda otomatisasi:
  ```python
  chrome_options.add_argument("--disable-blink-features=AutomationControlled")
  chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
  chrome_options.add_experimental_option("useAutomationExtension", False)
  driver.execute_cdp_cmd("Page.addScriptToEvaluateOnNewDocument", {
      "source": "Object.defineProperty(navigator, 'webdriver', {get: () => undefined})"
  })
  ```
- **Penyebab**: Halaman membutuhkan waktu terlalu lama untuk dimuat.
- **Solusi**: Tingkatkan nilai timeout atau periksa koneksi internet.

```
NoSuchElementException: Message: no such element: Unable to locate element
```
- **Penyebab**: Elemen yang dicari tidak ditemukan di halaman.
- **Solusi**: Perbarui selector atau periksa apakah struktur halaman telah berubah.

### Log Terminal

#### 1. Chrome WebDriver Logs
```
[INFO] Setting up Chrome WebDriver...
[INFO] Chrome options configured
[INFO] Initializing Chrome WebDriver...
[SUCCESS] Chrome WebDriver initialized successfully
```
- **Penjelasan**: Log ini menunjukkan proses inisialisasi Chrome WebDriver berhasil.

#### 2. DevTools Listening
```
DevTools listening on ws://127.0.0.1:...
```
- **Penjelasan**: Ini adalah pesan standar dari Chrome yang menunjukkan bahwa DevTools siap untuk debugging. Pesan ini normal dan dapat diabaikan.

#### 3. W3C Warnings
```
W3C Warnings...
```
- **Penjelasan**: Peringatan terkait standar W3C. Biasanya tidak mempengaruhi fungsionalitas dan dapat diabaikan.

## Fitur GUI

### 1. Input Fields
- **Email**: Field untuk memasukkan email login JobStreet.
- **Password**: Field untuk memasukkan password login JobStreet.
- **Pages to scrape**: Field untuk menentukan jumlah halaman yang akan di-scrape.

### 2. Dark Mode
- **Toggle**: Checkbox untuk mengaktifkan/menonaktifkan dark mode.
- **Persistent Setting**: Pengaturan dark mode disimpan dan akan diingat saat aplikasi dibuka kembali.

### 3. Progress Tracking
- **Progress Bar**: Menampilkan kemajuan proses scraping.
- **Log Output**: Menampilkan log detail tentang proses yang sedang berjalan.

### 4. Multithreading
- Proses scraping dijalankan di thread terpisah untuk menjaga GUI tetap responsif.
- Thread utama menangani interaksi pengguna sementara thread scraping menjalankan tugas berat.

## Cara Mengatasi Masalah Umum

### 1. GUI Tidak Muncul
- Pastikan PyQt5 terinstal dengan benar: `pip install PyQt5`
- Periksa apakah ada error di terminal saat menjalankan aplikasi.

### 2. Scraping Gagal
- Periksa koneksi internet.
- Pastikan kredensial login benar.
- Periksa apakah struktur situs JobStreet telah berubah.

### 3. Chrome Driver Error
- Pastikan Chrome terinstal.
- Pastikan versi ChromeDriver kompatibel dengan versi Chrome yang terinstal.
- Coba instal ulang ChromeDriver: `pip install webdriver-manager`

### 4. Data Tidak Tersimpan
- Periksa apakah folder aplikasi memiliki izin tulis.
- Pastikan file CSV tidak sedang dibuka oleh aplikasi lain.

## Pengembangan Lebih Lanjut

### 1. Fitur yang Dapat Ditambahkan
- Filter kandidat berdasarkan kriteria tertentu.
- Ekspor data ke format lain (Excel, JSON, dll).
- Penjadwalan scraping otomatis.
- Notifikasi email setelah scraping selesai.

### 2. Optimasi
- Implementasi caching untuk mengurangi beban server.
- Paralelisasi scraping untuk meningkatkan kecepatan.
- Penggunaan proxy untuk menghindari pembatasan rate.