#!/usr/bin/env python3
"""
Test script untuk memastikan GUI scraper berfungsi dengan baik
"""

import sys
import os
import time
from main import JobStreetScraperMain

def test_scraper_functionality():
    """Test the scraper functionality without GUI"""
    print("=== TESTING SCRAPER FUNCTIONALITY ===")
    
    # Test credentials
    email = "<EMAIL>"
    password = "wa9iH&F8s?64"
    job_page_link = "https://employer.jobstreetexpress.com/id/jobs/14a78030-fdc6-4a29-9471-ea4e3d6e419d-1739259131543/details"
    
    try:
        print(f"1. Initializing scraper...")
        scraper = JobStreetScraperMain(email, password, job_page_link)
        
        print(f"2. Setting up scraper...")
        if not scraper.setup():
            print("❌ Setup failed")
            return False
        print("✅ Setup successful")
        
        print(f"3. Testing login...")
        if not scraper.login_handler.login(scraper.login_url):
            print("❌ Login failed")
            return False
        print("✅ Login successful")
        
        print(f"4. Testing navigation to job page...")
        if not scraper.scraper.navigate_to_job_page(job_page_link):
            print("❌ Navigation failed")
            return False
        print("✅ Navigation successful")
        
        print(f"5. Testing candidate scraping...")
        # Test with limited pages for quick testing
        candidates = scraper.scraper.scrape_candidates()
        
        if not candidates:
            print("❌ No candidates found")
            return False
        
        print(f"✅ Scraping successful - Found {len(candidates)} candidates")
        
        # Show sample candidates
        print("\n📋 Sample candidates:")
        for i, candidate in enumerate(candidates[:5]):
            name = candidate.get('name', 'N/A')
            phone = candidate.get('phone', 'N/A')
            print(f"   {i+1}. {name} - {phone}")
        
        if len(candidates) > 5:
            print(f"   ... and {len(candidates) - 5} more candidates")
        
        print(f"\n6. Testing CSV export...")
        csv_file = "test_candidates.csv"
        try:
            from utils import save_to_csv
            fieldnames = ["name", "phone"]
            save_to_csv(candidates, fieldnames, csv_file)
            if os.path.exists(csv_file):
                print(f"✅ CSV export successful - {csv_file}")
                # Clean up test file
                os.remove(csv_file)
            else:
                print("❌ CSV file not created")
                return False
        except Exception as e:
            print(f"❌ CSV export failed: {e}")
            return False
        
        print(f"\n7. Cleaning up...")
        scraper.cleanup()
        print("✅ Cleanup successful")
        
        print(f"\n🎉 ALL TESTS PASSED!")
        print(f"📊 Total candidates scraped: {len(candidates)}")
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_gui_imports():
    """Test if GUI imports work correctly"""
    print("\n=== TESTING GUI IMPORTS ===")
    
    try:
        print("1. Testing GUI imports...")
        from gui import JobStreetScraperGUI, ScraperThread
        print("✅ GUI imports successful")
        
        print("2. Testing main scraper import...")
        from main import JobStreetScraperMain
        print("✅ Main scraper import successful")
        
        print("3. Testing scraper class import...")
        from scraper import JobStreetScraper
        print("✅ Scraper class import successful")
        
        print("4. Testing method availability...")
        # Check if the new method exists
        if hasattr(JobStreetScraper, 'scrape_candidates'):
            print("✅ scrape_candidates method available")
        else:
            print("❌ scrape_candidates method not found")
            return False
            
        if hasattr(JobStreetScraper, 'navigate_to_job_page'):
            print("✅ navigate_to_job_page method available")
        else:
            print("❌ navigate_to_job_page method not found")
            return False
            
        if hasattr(JobStreetScraper, 'get_candidates'):
            print("✅ get_candidates method available")
        else:
            print("❌ get_candidates method not found")
            return False
        
        print("🎉 ALL GUI IMPORT TESTS PASSED!")
        return True
        
    except Exception as e:
        print(f"❌ GUI import test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 Starting JobStreet Scraper Tests...")
    
    # Test GUI imports first
    gui_test_passed = test_gui_imports()
    
    if gui_test_passed:
        print("\n" + "="*50)
        # Test scraper functionality
        scraper_test_passed = test_scraper_functionality()
        
        if scraper_test_passed:
            print("\n" + "="*50)
            print("🎉 ALL TESTS COMPLETED SUCCESSFULLY!")
            print("✅ GUI is ready to use")
            print("✅ Scraper functionality is working")
            print("\n💡 You can now safely run the GUI using:")
            print("   python gui.py")
            print("   or")
            print("   .\\run_gui_updated.bat")
        else:
            print("\n❌ SCRAPER FUNCTIONALITY TESTS FAILED")
            sys.exit(1)
    else:
        print("\n❌ GUI IMPORT TESTS FAILED")
        sys.exit(1)
