import time
import os
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException, WebDriverException

# Import utility functions
from utils import find_element_with_multiple_selectors, take_screenshot

class LoginHandler:
    def __init__(self, driver, email, password):
        """
        Initialize the login handler
        
        Args:
            driver: WebDriver instance
            email: Email for login
            password: Password for login
        """
        self.driver = driver
        self.email = email
        self.password = password
    
    def login(self, login_url, max_retries=3):
        """
        Login to JobStreet Express
        
        Args:
            login_url: URL of the login page
            max_retries: Maximum number of login attempts
            
        Returns:
            bool: True if login successful, False otherwise
        """
        retry_count = 0
        
        while retry_count < max_retries:
            try:
                print(f"[INFO] Attempt {retry_count + 1}/{max_retries} - Navigating to login page...")
                
                # Navigate to login page
                self.driver.get(login_url)
                
                # Wait longer for page to fully load, including JavaScript
                print("[DEBUG] Menunggu halaman login dimuat sepenuhnya...")
                WebDriverWait(self.driver, 15).until(EC.presence_of_element_located((By.TAG_NAME, "body")))
                time.sleep(3) # Give more time for JavaScript rendering
                
                # Take screenshot to see current state
                take_screenshot(self.driver, f"login_page_loaded_attempt_{retry_count+1}.png")
                
                # Check if we're already logged in
                if "login" not in self.driver.current_url.lower():
                    print("[INFO] Sudah dalam keadaan login, melanjutkan...")
                    return True
                
                # Find and fill email field
                print("[DEBUG] Mencari email field...")
                email_field = self._find_email_field()
                if not email_field:
                    print("[ERROR] Email field tidak ditemukan setelah navigasi.")
                    take_screenshot(self.driver, "email_field_not_found_after_nav.png")
                    
                    # Try to find any input field as fallback
                    try:
                        all_inputs = self.driver.find_elements(By.TAG_NAME, "input")
                        print(f"[DEBUG] Ditemukan {len(all_inputs)} input fields di halaman")
                        for i, inp in enumerate(all_inputs[:5]):  # Check first 5 inputs
                            print(f"[DEBUG] Input {i+1}: type={inp.get_attribute('type')}, id={inp.get_attribute('id')}, name={inp.get_attribute('name')}, placeholder={inp.get_attribute('placeholder')}")
                    except:
                        pass
                    
                    retry_count += 1
                    if retry_count < max_retries:
                        print(f"[RETRY] Login gagal, mencoba lagi (attempt {retry_count+1}/{max_retries})...")
                        time.sleep(3)
                        continue
                    return False
                
                # Fill in email
                print("[DEBUG] Mengisi email...")
                email_field.clear()
                email_field.send_keys(self.email)
                print(f"[SUCCESS] Email terisi: {self.email}")
                time.sleep(1)  # Increased wait time for stability
                
                # Find and fill password field
                print("[DEBUG] Mencari password field...")
                password_field = self._find_password_field()
                if not password_field:
                    print("[ERROR] Password field tidak ditemukan!")
                    take_screenshot(self.driver, "password_field_error.png")
                    return False
                
                # Fill in password
                print("[DEBUG] Mengisi password...")
                password_field.clear()
                password_field.send_keys(self.password)
                print("[SUCCESS] Password terisi")
                time.sleep(1)  # Increased wait time for stability
                
                # Take screenshot before clicking login
                take_screenshot(self.driver, f"before_login_click_attempt_{retry_count+1}.png")
                
                # Find and click login button
                print("[DEBUG] Mencari tombol login...")
                login_button = self._find_login_button()
                if not login_button:
                    print("[ERROR] Tombol login tidak ditemukan!")
                    take_screenshot(self.driver, "login_button_error.png")
                    return False
                
                print("[DEBUG] Menunggu tombol login dapat diklik...")
                WebDriverWait(self.driver, 10).until(EC.element_to_be_clickable(login_button))
                
                print("[DEBUG] Mengklik tombol login dengan multiple methods...")
                # Metode 1: Klik normal
                try:
                    login_button.click()
                    print("[SUCCESS] Tombol login diklik dengan metode normal")
                except Exception as e:
                    print(f"[WARNING] Klik normal gagal: {e}")
                    
                    # Metode 2: JavaScript click
                    try:
                        print("[DEBUG] Mencoba JavaScript click...")
                        self.driver.execute_script("arguments[0].click();", login_button)
                        print("[SUCCESS] Tombol login diklik dengan JavaScript")
                    except Exception as js_e:
                        print(f"[WARNING] JavaScript click gagal: {js_e}")
                        
                        # Metode 3: Action chains
                        try:
                            print("[DEBUG] Mencoba Action Chains...")
                            from selenium.webdriver.common.action_chains import ActionChains
                            actions = ActionChains(self.driver)
                            actions.move_to_element(login_button).click().perform()
                            print("[SUCCESS] Tombol login diklik dengan Action Chains")
                        except Exception as ac_e:
                            print(f"[ERROR] Semua metode klik gagal: {ac_e}")
                            return False
                
                # Metode 4: Submit form jika tombol berada dalam form
                try:
                    print("[DEBUG] Mencoba submit form...")
                    form = self.driver.find_element(By.XPATH, "//form")
                    if form:
                        form.submit()
                        print("[SUCCESS] Form disubmit langsung")
                except Exception as form_e:
                    print(f"[WARNING] Form submit gagal: {form_e}")
                    
                    # Metode 5: Tekan Enter pada field password
                    try:
                        print("[DEBUG] Mencoba tekan Enter pada field password...")
                        password_field = self._find_password_field()
                        if password_field:
                            from selenium.webdriver.common.keys import Keys
                            password_field.send_keys(Keys.RETURN)
                            print("[SUCCESS] Enter ditekan pada field password")
                    except Exception as enter_e:
                        print(f"[WARNING] Tekan Enter gagal: {enter_e}")
                
                # Tambahan: Tunggu sebentar setelah klik
                time.sleep(3)
                
                # Wait for login to complete with better condition
                print("[DEBUG] Menunggu login selesai...")
                try:
                    # Tunggu URL berubah (indikasi paling jelas login berhasil)
                    WebDriverWait(self.driver, 30).until(  # Increased timeout
                        lambda driver: "login" not in driver.current_url
                    )
                    print(f"[SUCCESS] URL berubah, login berhasil: {self.driver.current_url}")
                    take_screenshot(self.driver, "login_success.png")
                    return True
                except TimeoutException:
                    print("[WARNING] URL tidak berubah, memeriksa elemen dashboard...")
                    
                    # Coba cari elemen yang hanya muncul setelah login berhasil
                    try:
                        dashboard_elements = [
                            (By.XPATH, "//h1[contains(text(), 'Dashboard')]"),
                            (By.XPATH, "//div[@data-testid='dashboard-page']"),
                            (By.XPATH, "//div[contains(@class, 'dashboard')]"),
                            (By.XPATH, "//div[contains(@class, 'home-page')]"),
                            (By.XPATH, "//a[contains(text(), 'Logout') or contains(text(), 'Keluar')]"),
                            (By.XPATH, "//div[contains(@class, 'user-profile')]"),
                            (By.XPATH, "//div[contains(@class, 'account-menu')]"),
                            (By.XPATH, "//div[contains(text(), 'Dashboard')]"),
                            (By.XPATH, "//div[contains(@class, 'welcome-message')]"),
                        ]
                        
                        for selector_type, selector in dashboard_elements:
                            try:
                                element = WebDriverWait(self.driver, 5).until(
                                    EC.presence_of_element_located((selector_type, selector))
                                )
                                print(f"[SUCCESS] Elemen dashboard ditemukan: {selector}")
                                take_screenshot(self.driver, "login_success_element.png")
                                return True
                            except:
                                continue
                        
                        print("[ERROR] Tidak dapat menemukan elemen dashboard")
                        take_screenshot(self.driver, "login_no_dashboard.png")
                    except Exception as e:
                        print(f"[ERROR] Error saat mencari elemen dashboard: {e}")
                    
                    # Check current URL and page state
                    current_url = self.driver.current_url
                    print(f"[DEBUG] Current URL after timeout: {current_url}")
                    
                    # Check if we're still on login page
                    if "login" in current_url.lower():
                        print("[ERROR] Masih di halaman login, kemungkinan login gagal")
                        self._check_error_messages()
                        
                        # Check for any error indicators
                        try:
                            page_source = self.driver.page_source.lower()
                            if "error" in page_source or "gagal" in page_source or "salah" in page_source:
                                print("[ERROR] Ditemukan indikator error di halaman")
                        except:
                            pass
                        
                        # Coba refresh halaman dan periksa URL lagi
                        try:
                            print("[DEBUG] Mencoba refresh halaman...")
                            self.driver.refresh()
                            time.sleep(3)
                            
                            if "login" not in self.driver.current_url:
                                print("[SUCCESS] URL berubah setelah refresh, login berhasil")
                                take_screenshot(self.driver, "login_success_after_refresh.png")
                                return True
                        except Exception as e:
                            print(f"[ERROR] Error saat refresh: {e}")
                        
                        # Coba klik lagi dengan JavaScript jika masih di halaman login
                        try:
                            print("[DEBUG] Mencoba klik login lagi dengan JavaScript...")
                            login_button = self._find_login_button()
                            if login_button:
                                self.driver.execute_script("arguments[0].click();", login_button)
                                time.sleep(5)
                                
                                if "login" not in self.driver.current_url:
                                    print("[SUCCESS] URL berubah setelah klik ulang, login berhasil")
                                    take_screenshot(self.driver, "login_success_after_reclick.png")
                                    return True
                        except Exception as e:
                            print(f"[ERROR] Error saat klik ulang: {e}")
                        
                        retry_count += 1
                        if retry_count < max_retries:
                            print(f"[RETRY] Mencoba login lagi (attempt {retry_count+1}/{max_retries})...")
                            time.sleep(3)
                            continue
                        return False
                    else:
                        print("[INFO] URL sudah berubah, menganggap login berhasil")
                        print(f"[SUCCESS] Login berhasil! URL: {current_url}")
                        take_screenshot(self.driver, "login_success_url_changed.png")
                        return True
                
            except Exception as e:
                print(f"[ERROR] Error saat login: {e}")
                import traceback
                print(f"[DEBUG] Traceback: {traceback.format_exc()}")
                take_screenshot(self.driver, f"login_error_attempt_{retry_count+1}.png")
                retry_count += 1
                if retry_count < max_retries:
                    print(f"[RETRY] Error terjadi, mencoba login lagi (attempt {retry_count+1}/{max_retries})...")
                    time.sleep(3)
                    continue
                return False
        
        print(f"[ERROR] Login gagal setelah {max_retries} percobaan")
        return False
    
    def _find_email_field(self):
        """
        Find the email input field using multiple selectors
        
        Returns:
            WebElement if found, None otherwise
        """
        email_selectors = [
            (By.ID, "login-form-email"),  # Exact ID from provided element
            (By.CSS_SELECTOR, "input#login-form-email"),  # CSS selector with ID
            (By.XPATH, "//input[@data-testid='login-form-email']"),  # Test ID selector
            (By.XPATH, "//input[@id='login-form-email']"),  # XPath with ID
            (By.ID, "email"),  # Fallback selectors
            (By.CSS_SELECTOR, "input.css-11aywtz"),
            (By.XPATH, "//input[@type='email']")
        ]
        
        print("[DEBUG] Mencari field email...")
        email_field = find_element_with_multiple_selectors(self.driver, email_selectors, wait_time=5)
        
        if not email_field:
            print("[ERROR] Field email tidak ditemukan!")
            take_screenshot(self.driver, "email_field_not_found.png")
        else:
            print("[SUCCESS] Field email ditemukan")
        
        return email_field
    
    def _find_password_field(self):
        """
        Find the password input field using multiple selectors
        
        Returns:
            WebElement if found, None otherwise
        """
        password_selectors = [
            (By.ID, "login-form-password"),  # Exact ID from provided element
            (By.CSS_SELECTOR, "input#login-form-password"),  # CSS selector with ID
            (By.XPATH, "//input[@data-testid='login-form-password']"),  # Test ID selector
            (By.XPATH, "//input[@id='login-form-password']"),  # XPath with ID
            (By.ID, "password"),  # Fallback selectors
            (By.CSS_SELECTOR, "input[type='password']"),
            (By.XPATH, "//div[contains(text(),'Kata sandi')]/following::input[@type='password']"),
            (By.XPATH, "//input[@type='password']")
        ]
        
        print("[DEBUG] Mencari field password...")
        password_field = find_element_with_multiple_selectors(self.driver, password_selectors, wait_time=5)
        
        if not password_field:
            print("[ERROR] Field password tidak ditemukan!")
            take_screenshot(self.driver, "password_field_not_found.png")
        else:
            print("[SUCCESS] Field password ditemukan")
        
        return password_field
    
    def _find_login_button(self):
        """
        Find the login button using multiple selectors
        
        Returns:
            WebElement if found, None otherwise
        """
        login_button_selectors = [
            (By.XPATH, "//button[@data-testid='login-action']"), # Specific selector for the provided button
            (By.XPATH, "//button[contains(text(), 'Masuk sebagai Perusahaan')]"),
            (By.XPATH, "//button[text()='Masuk sebagai Perusahaan']"),
            (By.XPATH, "//button[@type='submit']"),  # Most common submit button
            (By.CSS_SELECTOR, "button[type='submit']"),
            (By.CSS_SELECTOR, "button.css-175oi2r"),  # Class-based selector
            (By.XPATH, "//button[contains(@class, 'submit')]"),
            (By.XPATH, "//button[contains(@class, 'login')]"),
            (By.XPATH, "//button[contains(text(), 'Masuk')]"),  # Generic login button
            (By.XPATH, "//button[contains(text(), 'Login')]"),
            (By.XPATH, "//button[contains(text(), 'Sign In')]"),
            (By.XPATH, "//form//button")  # Any button inside form
        ]
        
        print("[DEBUG] Mencari tombol login...")
        login_button = find_element_with_multiple_selectors(self.driver, login_button_selectors, wait_time=5)
        
        if not login_button:
            print("[ERROR] Tombol login tidak ditemukan!")
            take_screenshot(self.driver, "login_button_not_found.png")
        else:
            print("[SUCCESS] Tombol login ditemukan")
        
        return login_button
    
    def _check_error_messages(self):
        """
        Check for error messages on the login page
        """
        try:
            error_messages = self.driver.find_elements(By.XPATH, "//div[contains(@class, 'error')] | //p[contains(@class, 'error')] | //span[contains(@class, 'error')]")
            if error_messages:
                for error in error_messages:
                    print(f"Error message found: {error.text}")
        except Exception as e:
            print(f"Error checking for error messages: {e}")