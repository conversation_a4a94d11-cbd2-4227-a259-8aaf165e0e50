import os
import sys
import time
import csv
import signal
import traceback
from selenium import webdriver

# Import custom modules
from utils import setup_chrome_driver, take_screenshot, save_to_csv
from login_handler import <PERSON>ginHandler
from scraper import Scraper
from pagination_handler import PaginationHandler

class JobStreetScraperMain:
    def __init__(self, email, password, job_page_link, login_url="https://employer.jobstreetexpress.com/id/login"):
        """
        Initialize the JobStreet scraper
        
        Args:
            email: Email for login
            password: Password for login
            job_page_link: URL of the job page to scrape
            login_url: URL of the login page
        """
        self.email = email
        self.password = password
        self.job_page_link = job_page_link
        self.login_url = login_url
        self.driver = None
        self.login_handler = None
        self.scraper = None
        self.pagination_handler = None
        self.candidates = []
    
    def setup(self):
        """
        Set up the scraper components
        
        Returns:
            bool: True if setup successful, False otherwise
        """
        try:
            # Setup Chrome driver
            self.driver = setup_chrome_driver()
            if not self.driver:
                print("Failed to set up Chrome driver")
                return False
            
            # Initialize components
            self.login_handler = LoginHandler(self.driver, self.email, self.password)
            self.scraper = Scraper(self.driver)
            self.pagination_handler = PaginationHandler(self.driver)
            
            return True
            
        except Exception as e:
            print(f"Error setting up scraper: {e}")
            traceback.print_exc()
            return False
    
    def run(self):
        """
        Run the scraper
        
        Returns:
            list: List of scraped candidates
        """
        try:
            # Setup
            if not self.setup():
                print("Setup failed. Exiting...")
                return []
            
            # Login
            print("Logging in...")
            if not self.login_handler.login(self.login_url):
                print("Login failed. Exiting...")
                self.cleanup()
                return []
            
            print("[INFO] Waiting for 10 seconds after successful login...")
            time.sleep(10) # Delay after login before navigating to job page
            
            # Navigate to job page
            print("Navigating to job page...")
            if not self.scraper.navigate_to_job_page(self.job_page_link):
                print("Failed to navigate to job page. Exiting...")
                self.cleanup()
                return []
            
            # Scrape candidates
            print("Scraping candidates...")
            page_candidates = self.scraper.scrape_candidates()
            print(f"Found {len(page_candidates)} candidates.")
            
            # Get all scraped candidates
            self.candidates = self.scraper.get_candidates()
            print(f"\nTotal candidates scraped: {len(self.candidates)}")
            
            # Save to CSV
            if self.candidates:
                csv_file = "candidates.csv"
                fieldnames = ["name", "phone"]
                save_to_csv(self.candidates, fieldnames, csv_file)
                print(f"Saved {len(self.candidates)} candidates to {csv_file}")
            
            return self.candidates
            
        except Exception as e:
            print(f"Error running scraper: {e}")
            traceback.print_exc()
            take_screenshot(self.driver, "scraper_error.png")
            return []
        finally:
            self.cleanup()
    
    def cleanup(self):
        """
        Clean up resources
        """
        try:
            if self.driver:
                self.driver.quit()
                print("WebDriver closed")
        except Exception as e:
            print(f"Error closing WebDriver: {e}")

def signal_handler(sig, frame):
    """
    Handle interrupt signals
    """
    print('\nYou pressed Ctrl+C! Exiting gracefully...')
    sys.exit(0)

def main():
    """
    Main function
    """
    # Register signal handler for Ctrl+C
    signal.signal(signal.SIGINT, signal_handler)
    
    try:
        # Get credentials
        email = input("Enter your email: ")
        password = input("Enter your password: ")
        job_page_link = input("Enter the job page link to scrape: ")
        
        # Initialize and run scraper
        scraper = JobStreetScraperMain(email, password, job_page_link)
        scraper.run()
        
    except Exception as e:
        print(f"An error occurred: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    main()